#!/usr/bin/env node

/**
 * Debug script to check why cron jobs aren't running
 *
 * This script checks:
 * 1. Circuit breaker status
 * 2. Redis connectivity
 * 3. Worker health status
 * 4. System resources
 * 5. Cron job configuration
 */

const { logger } = require("../utils/logger.js");
const { getSharedCircuitBreaker } = require("../utils/sharedCircuitBreaker.js");
const { getContainerMetrics } = require("../utils/containerMetrics.js");
const { redis } = require("../../workers/redis.js");

async function debugCronStatus() {
  logger.info("🔍 Starting cron job debug analysis");

  try {
    // 1. Check Redis connectivity
    logger.info("📡 Testing Redis connectivity...");
    try {
      await redis.ping();
      logger.info("✅ Redis is connected and responding");
    } catch (error) {
      logger.error("❌ Redis connection failed:", error);
      return;
    }

    // 2. Check circuit breaker status
    logger.info("🧠 Checking circuit breaker status...");
    try {
      const circuitBreaker = getSharedCircuitBreaker();
      const isClosed = await circuitBreaker.isClosed();
      const state = circuitBreaker.getState();

      logger.info(`🔧 Circuit breaker state: ${state}`);
      logger.info(`🔧 Circuit breaker closed (jobs allowed): ${isClosed}`);

      if (!isClosed) {
        logger.warn(
          "⚠️ Circuit breaker is OPEN - this may be preventing cron jobs from running!"
        );
      }
    } catch (error) {
      logger.error("❌ Failed to check circuit breaker:", error);
    }

    // 3. Check system resources
    logger.info("📊 Checking system resources...");
    try {
      const containerMetrics = await getContainerMetrics();

      if (containerMetrics) {
        logger.info(
          `💾 Memory usage: ${containerMetrics.memoryUsagePercent.toFixed(2)}%`
        );
        logger.info(
          `🖥️ CPU usage: ${containerMetrics.cpuUsagePercent.toFixed(2)}%`
        );

        if (containerMetrics.memoryUsagePercent > 80) {
          logger.warn("⚠️ High memory usage detected!");
        }

        if (containerMetrics.cpuUsagePercent > 80) {
          logger.warn("⚠️ High CPU usage detected!");
        }
      } else {
        logger.warn("⚠️ Container metrics not available");
      }
    } catch (error) {
      logger.error("❌ Failed to get system metrics:", error);
    }

    // 4. Check worker health data in Redis
    logger.info("🏥 Checking worker health data in Redis...");
    try {
      const healthData = await redis.hgetall("worker:health");
      const metricsData = await redis.hgetall("worker:metrics");
      const circuitData = await redis.hgetall("worker:circuit");

      logger.info(`📋 Health data entries: ${Object.keys(healthData).length}`);
      logger.info(
        `📋 Metrics data entries: ${Object.keys(metricsData).length}`
      );
      logger.info(
        `📋 Circuit data entries: ${Object.keys(circuitData).length}`
      );

      // Show health status for each worker type
      for (const [workerType, healthJson] of Object.entries(healthData)) {
        try {
          const health = JSON.parse(healthJson);
          logger.info(
            `🔧 ${workerType}: ${health.healthy ? "HEALTHY" : "UNHEALTHY"} (${health.status})`
          );
        } catch (error) {
          logger.warn(`⚠️ Failed to parse health data for ${workerType}`);
        }
      }

      // Show circuit status for each worker type
      for (const [workerType, circuitJson] of Object.entries(circuitData)) {
        try {
          const circuit = JSON.parse(circuitJson);
          logger.info(
            `🧠 ${workerType} circuit: ${circuit.state} (failures: ${circuit.failureCount})`
          );
        } catch (error) {
          logger.warn(`⚠️ Failed to parse circuit data for ${workerType}`);
        }
      }
    } catch (error) {
      logger.error("❌ Failed to check Redis health data:", error);
    }

    // 5. Check if any jobs are currently marked as running
    logger.info("🏃 Checking for running jobs...");
    try {
      const runningJobsData = await redis.hgetall("cron:running_jobs");
      logger.info(
        `📋 Running jobs data: ${JSON.stringify(runningJobsData, null, 2)}`
      );

      const runningCount = Object.values(runningJobsData).filter(
        (status) => status === "true"
      ).length;
      if (runningCount > 0) {
        logger.warn(
          `⚠️ ${runningCount} jobs are marked as currently running - this may prevent new jobs from starting`
        );
      } else {
        logger.info("✅ No jobs are currently marked as running");
      }
    } catch (error) {
      logger.warn("⚠️ Could not check running jobs status:", error);
    }

    // 6. Summary and recommendations
    logger.info("📋 Debug Summary:");
    logger.info("  • Redis connectivity: Check logs above");
    logger.info("  • Circuit breaker status: Check logs above");
    logger.info("  • System resources: Check logs above");
    logger.info("  • Worker health: Check logs above");

    logger.info("🔧 Next steps:");
    logger.info(
      "  1. If circuit breaker is OPEN, check why and consider resetting it"
    );
    logger.info(
      "  2. If jobs are stuck as 'running', clear the running jobs status"
    );
    logger.info("  3. Check system resources and reduce load if necessary");
    logger.info("  4. Verify cron job schedules are correct");
  } catch (error) {
    logger.error("❌ Debug analysis failed:", error);
  }
}

async function main() {
  try {
    await debugCronStatus();
    process.exit(0);
  } catch (error) {
    logger.error("❌ Debug script failed:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

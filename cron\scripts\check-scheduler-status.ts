// cron/scripts/check-scheduler-status.ts
// <PERSON>ript to check the status of the scheduler service and cron jobs

import { logger } from "../utils/logger";
import { PrismaClient } from "@prisma/client";
import { startScheduledJobs } from "../jobs/scheduledJobs";
import os from "os";
import { CircuitBreaker, CircuitState } from "../utils/circuitBreaker";

const prisma = new PrismaClient();

// Function to get detailed system resource information
function getSystemResourceInfo() {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;
  
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const loadPerCpu = loadAvg / cpuCount;
  const cpuUsagePercent = loadPerCpu * 100;
  
  return {
    memory: {
      total: formatBytes(totalMemory),
      free: formatBytes(freeMemory),
      used: formatBytes(usedMemory),
      usagePercent: memoryUsagePercent.toFixed(2) + "%"
    },
    cpu: {
      count: cpuCount,
      loadAverage: loadAvg.toFixed(2),
      loadPerCpu: loadPerCpu.toFixed(2),
      usagePercent: cpuUsagePercent.toFixed(2) + "%"
    },
    process: {
      pid: process.pid,
      uptime: process.uptime().toFixed(2) + "s",
      memoryUsage: {
        rss: formatBytes(process.memoryUsage().rss),
        heapTotal: formatBytes(process.memoryUsage().heapTotal),
        heapUsed: formatBytes(process.memoryUsage().heapUsed),
        external: formatBytes(process.memoryUsage().external)
      }
    },
    os: {
      platform: process.platform,
      release: os.release(),
      uptime: (os.uptime() / 60 / 60).toFixed(2) + " hours"
    }
  };
}

// Helper function to format bytes to human-readable format
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Check the last run time of each job
async function checkJobLastRunTimes() {
  try {
    logger.info("📊 Checking last run times for jobs...");
    
    // Get the last run time for each job type from the database
    const jobStats = await prisma.jobStats.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      take: 50 // Get the last 50 job runs
    });
    
    // Group by job type and get the most recent for each
    const jobTypeMap = new Map<string, typeof jobStats[0]>();
    
    for (const stat of jobStats) {
      if (!jobTypeMap.has(stat.jobType) || 
          new Date(stat.createdAt) > new Date(jobTypeMap.get(stat.jobType)!.createdAt)) {
        jobTypeMap.set(stat.jobType, stat);
      }
    }
    
    // Display the results
    logger.info("📅 Last run times for each job type:");
    
    if (jobTypeMap.size === 0) {
      logger.warn("⚠️ No job statistics found in the database");
    } else {
      for (const [jobType, stat] of jobTypeMap.entries()) {
        const lastRunTime = new Date(stat.createdAt);
        const now = new Date();
        const hoursSinceLastRun = (now.getTime() - lastRunTime.getTime()) / (1000 * 60 * 60);
        
        logger.info(`  • ${jobType}: ${lastRunTime.toLocaleString()} (${hoursSinceLastRun.toFixed(1)} hours ago)`);
        logger.info(`    - Duration: ${stat.durationMs / 1000}s, Success: ${stat.success}, Items: ${stat.itemsProcessed}`);
      }
    }
    
    return jobTypeMap;
  } catch (error) {
    logger.error("❌ Error checking job last run times:", error);
    return new Map();
  }
}

// Check if the circuit breaker might be preventing jobs from running
async function checkCircuitBreakerStatus() {
  logger.info("🔍 Checking circuit breaker status...");
  
  // Create a test circuit breaker to check system resources
  const circuitBreaker = new CircuitBreaker({
    memoryThresholdPercent: 85,
    cpuThresholdPercent: 85,
    errorThresholdCount: 5,
    checkIntervalMs: 1000, // Check immediately
    onStateChange: (oldState, newState) => {
      logger.info(`🔄 Circuit breaker state changed from ${oldState} to ${newState}`);
    }
  });
  
  // Wait a moment for the circuit breaker to check resources
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Get the current state
  const state = circuitBreaker.getState();
  logger.info(`🔄 Current circuit breaker state: ${state}`);
  
  // Check system resources
  const resourceInfo = getSystemResourceInfo();
  logger.info(`📊 Current system resources:`);
  logger.info(`  • Memory usage: ${resourceInfo.memory.usagePercent}`);
  logger.info(`  • CPU usage: ${resourceInfo.cpu.usagePercent}`);
  
  // Stop monitoring
  circuitBreaker.stopMonitoring();
  
  return {
    state,
    resourceInfo
  };
}

// Main function to check scheduler status
async function checkSchedulerStatus() {
  logger.info("🔍 Starting scheduler status check...");
  
  try {
    // Check system resources
    const resourceInfo = getSystemResourceInfo();
    logger.info(`📊 System resource information: ${JSON.stringify(resourceInfo, null, 2)}`);
    
    // Check job last run times
    const jobLastRunTimes = await checkJobLastRunTimes();
    
    // Check circuit breaker status
    const circuitBreakerStatus = await checkCircuitBreakerStatus();
    
    // Provide a summary and recommendations
    logger.info("📋 Scheduler Status Summary:");
    
    if (circuitBreakerStatus.state !== CircuitState.CLOSED) {
      logger.warn("⚠️ Circuit breaker is not in CLOSED state, which may be preventing jobs from running");
      logger.warn("   This could be due to high system resource usage or accumulated errors");
    } else {
      logger.info("✅ Circuit breaker is in CLOSED state and should allow jobs to run");
    }
    
    // Check if any jobs haven't run in the last 24 hours
    const jobsNotRunRecently = [];
    for (const [jobType, stat] of jobLastRunTimes.entries()) {
      const lastRunTime = new Date(stat.createdAt);
      const now = new Date();
      const hoursSinceLastRun = (now.getTime() - lastRunTime.getTime()) / (1000 * 60 * 60);
      
      if (hoursSinceLastRun > 24) {
        jobsNotRunRecently.push({ jobType, hoursSinceLastRun });
      }
    }
    
    if (jobsNotRunRecently.length > 0) {
      logger.warn("⚠️ The following jobs haven't run in the last 24 hours:");
      for (const job of jobsNotRunRecently) {
        logger.warn(`   • ${job.jobType}: ${job.hoursSinceLastRun.toFixed(1)} hours ago`);
      }
    } else if (jobLastRunTimes.size > 0) {
      logger.info("✅ All jobs have run within the last 24 hours");
    }
    
    // Provide recommendations
    logger.info("🔧 Recommendations:");
    
    if (circuitBreakerStatus.state !== CircuitState.CLOSED) {
      logger.info("1. Restart the scheduler service to reset the circuit breaker state");
      logger.info("2. Check system resources and reduce load if necessary");
      logger.info("3. Review logs for errors that might be triggering the circuit breaker");
    } else if (jobsNotRunRecently.length > 0) {
      logger.info("1. Restart the scheduler service");
      logger.info("2. Check logs for errors during job execution");
      logger.info("3. Try running one of the jobs manually to see if it completes successfully");
    } else {
      logger.info("✅ No issues detected with the scheduler service");
    }
    
  } catch (error) {
    logger.error("❌ Error checking scheduler status:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkSchedulerStatus()
  .then(() => {
    logger.info("✅ Scheduler status check completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Scheduler status check failed with error:", error);
    process.exit(1);
  });

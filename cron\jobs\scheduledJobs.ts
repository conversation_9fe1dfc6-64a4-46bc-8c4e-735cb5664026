// cron/jobs/scheduledJobs.ts

import { CronJob } from "cron";
import { logger, globalJobStats } from "../utils/logger";
import { spawn } from "child_process";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import { withCircuitBreaker } from "../utils/sharedCircuitBreaker";

// Define job types for type safety
type JobType =
  | "parallelJobScraper"
  | "enrichJobDetails"
  | "scrapeJobDetails"
  | "checkExperienceRequirements"
  | "domainUpdate"
  | "updateJobMatches"
  | "dailySummary"
  | "marketAnalytics";

// Removed extractJobCount function as it's now replaced by extractJobStats

/**
 * Extract job statistics from log output based on job type
 * @param jobName The type of job
 * @param logs Array of log messages
 * @returns Object with jobsProcessed, jobsSucceeded, and jobsFailed
 */
export function extractJobStats(
  jobName: JobType,
  logs: string[]
): {
  jobsProcessed: number;
  jobsSucceeded: number;
  jobsFailed: number;
} {
  // Default values
  let jobsProcessed = 0;
  let jobsSucceeded = 0;
  let jobsFailed = 0;

  try {
    // Join logs into a single string for easier searching
    const logText = logs.join("\n");

    // First, check for standardized job stats format
    const statsRegex =
      /\[JOB_STATS\] (\w+) \| PROCESSED:(\d+) \| SUCCEEDED:(\d+) \| FAILED:(\d+)/;
    const statsMatch = statsRegex.exec(logText);
    if (statsMatch) {
      // Extract values from standardized format
      const loggedJobType = statsMatch[1];
      const processed = parseInt(statsMatch[2], 10);
      const succeeded = parseInt(statsMatch[3], 10);
      const failed = parseInt(statsMatch[4], 10);

      // Only use these values if they're for the current job type
      if (loggedJobType.toLowerCase() === jobName.toLowerCase()) {
        jobsProcessed = processed;
        jobsSucceeded = succeeded;
        jobsFailed = failed;

        // If we found standardized stats, return them immediately
        return {
          jobsProcessed,
          jobsSucceeded,
          jobsFailed,
        };
      }
    }

    // If no standardized format found, fall back to job-specific patterns
    // Different job types have different log patterns
    switch (jobName) {
      case "updateJobMatches": {
        // Extract profiles count
        const profilesRegex = /Found\s+(\d+)\s+profiles/;
        const profilesMatch = profilesRegex.exec(logText);
        if (profilesMatch?.[1]) {
          jobsProcessed = parseInt(profilesMatch[1], 10);
        }

        // Extract successful job matches
        const matchesPattern = /Saved\s+(\d+)\s+job matches/g;
        const matchesMatches = [...logText.matchAll(matchesPattern)];
        jobsSucceeded = matchesMatches.reduce((total, match) => {
          return total + (match[1] ? parseInt(match[1], 10) : 0);
        }, 0);

        // Extract errors
        const errorPattern = /Error processing profile/g;
        const errorMatches = logText.match(errorPattern);
        jobsFailed = errorMatches ? errorMatches.length : 0;
        break;
      }

      case "domainUpdate": {
        // Extract companies processed
        const companiesRegex = /Updated domains for\s+(\d+)\/(\d+)\s+companies/;
        const companiesMatch = companiesRegex.exec(logText);
        if (companiesMatch?.[2]) {
          jobsProcessed = parseInt(companiesMatch[2], 10);
        }
        if (companiesMatch?.[1]) {
          jobsSucceeded = parseInt(companiesMatch[1], 10);
        }

        // Try alternative patterns
        if (jobsProcessed === 0) {
          // Look for processing batch information
          const batchRegex = /Processing batch\s+(\d+)\/(\d+)/;
          const batchMatch = batchRegex.exec(logText);
          if (batchMatch?.[2]) {
            jobsProcessed = parseInt(batchMatch[2], 10);
          }

          // Count successful domain updates
          const successPattern = /Updated company .+ with domain/g;
          const successMatches = logText.match(successPattern);
          if (successMatches) {
            jobsSucceeded = successMatches.length;
          }

          // Count verified domains
          const verifiedPattern = /Verified domain for/g;
          const verifiedMatches = logText.match(verifiedPattern);
          if (verifiedMatches) {
            // This is another way to count successes
            jobsSucceeded = Math.max(jobsSucceeded, verifiedMatches.length);
          }
        }

        // Count failures
        const noDomainsPattern =
          /No valid domains found for|Could not find domain for/g;
        const noDomainsMatches = logText.match(noDomainsPattern);
        if (noDomainsMatches) {
          jobsFailed = noDomainsMatches.length;
        } else {
          // Calculate failures if not explicitly found
          jobsFailed = jobsProcessed - jobsSucceeded;
        }
        break;
      }

      case "scrapeJobDetails": {
        // Extract jobs processed
        const jobsRegex = /Processing\s+(\d+)\s+jobs/;
        const jobsMatch = jobsRegex.exec(logText);
        if (jobsMatch?.[1]) {
          jobsProcessed = parseInt(jobsMatch[1], 10);
        }

        // Try alternative patterns for jobs processed
        if (jobsProcessed === 0) {
          // Look for job details scraper summary
          const summaryRegex = /Job Details Scraper processed (\d+) jobs/;
          const summaryMatch = summaryRegex.exec(logText);
          if (summaryMatch?.[1]) {
            jobsProcessed = parseInt(summaryMatch[1], 10);
          }

          // Look for batch processing information
          const batchRegex = /Processing batch (\d+)\/\d+ with (\d+) jobs/;
          const batchMatch = batchRegex.exec(logText);
          if (batchMatch?.[2]) {
            // This might be partial, but better than nothing
            jobsProcessed = parseInt(batchMatch[2], 10);
          }
        }

        // Extract successful jobs
        const successPattern =
          /Successfully processed job|Successfully scraped details for job/g;
        const successMatches = logText.match(successPattern);
        jobsSucceeded = successMatches ? successMatches.length : 0;

        // Look for explicit success count
        const successCountRegex = /(\d+) jobs processed successfully/;
        const successCountMatch = successCountRegex.exec(logText);
        if (successCountMatch?.[1]) {
          jobsSucceeded = parseInt(successCountMatch[1], 10);
        }

        // Extract failures
        const failurePattern =
          /Failed to process job|Error scraping job details|Skipping job/g;
        const failureMatches = logText.match(failurePattern);
        if (failureMatches) {
          jobsFailed = failureMatches.length;
        } else {
          // Calculate failures if not explicitly found
          jobsFailed = jobsProcessed - jobsSucceeded;
        }

        // Look for explicit failure count
        const failureCountRegex = /(\d+) jobs failed/;
        const failureCountMatch = failureCountRegex.exec(logText);
        if (failureCountMatch?.[1]) {
          jobsFailed = parseInt(failureCountMatch[1], 10);
        }
        break;
      }

      case "checkExperienceRequirements": {
        // Extract jobs processed
        let expJobsMatch;
        let expBatchMatch;
        let expTotalMatch;
        let expUpdatedPattern;
        let expUpdatedMatches;
        let expSuccessCountMatch;
        let expFailurePattern;
        let expFailureMatches;
        let expFailureCountMatch;

        expJobsMatch = RegExp.prototype.exec.call(
          /Checking\s+(\d+)\s+jobs/,
          logText
        );
        if (expJobsMatch?.[1]) {
          jobsProcessed = parseInt(expJobsMatch[1], 10);
        }

        // Try alternative patterns for jobs processed
        if (jobsProcessed === 0) {
          // Look for batch processing information
          expBatchMatch = RegExp.prototype.exec.call(
            /Processing batch of (\d+) jobs/,
            logText
          );
          if (expBatchMatch?.[1]) {
            jobsProcessed = parseInt(expBatchMatch[1], 10);
          }

          // Look for total jobs count
          expTotalMatch = RegExp.prototype.exec.call(
            /Total jobs to check: (\d+)/,
            logText
          );
          if (expTotalMatch?.[1]) {
            jobsProcessed = parseInt(expTotalMatch[1], 10);
          }
        }

        // Extract successful updates
        expUpdatedPattern =
          /Updated experience requirements|Successfully identified experience/g;
        expUpdatedMatches = logText.match(expUpdatedPattern);
        jobsSucceeded = expUpdatedMatches ? expUpdatedMatches.length : 0;

        // Look for explicit success count
        expSuccessCountMatch = RegExp.prototype.exec.call(
          /(\d+) jobs updated successfully/,
          logText
        );
        if (expSuccessCountMatch?.[1]) {
          jobsSucceeded = parseInt(expSuccessCountMatch[1], 10);
        }

        // Extract failures
        expFailurePattern =
          /Failed to update experience|Could not determine experience|Skipping job/g;
        expFailureMatches = logText.match(expFailurePattern);
        if (expFailureMatches) {
          jobsFailed = expFailureMatches.length;
        } else {
          // Calculate failures if not explicitly found
          jobsFailed = jobsProcessed - jobsSucceeded;
        }

        // Look for explicit failure count
        expFailureCountMatch = RegExp.prototype.exec.call(
          /(\d+) jobs failed/,
          logText
        );
        if (expFailureCountMatch?.[1]) {
          jobsFailed = parseInt(expFailureCountMatch[1], 10);
        }
        break;
      }

      case "enrichJobDetails": {
        // Extract jobs processed
        let enrichJobsMatch;
        let batchMatch;
        let totalMatch;
        let summaryMatch;
        let enrichedPattern;
        let enrichedMatches;
        let enrichSuccessMatch;
        let enrichFailPattern;
        let enrichFailMatches;
        let enrichFailCountMatch;

        enrichJobsMatch = RegExp.prototype.exec.call(
          /Enriching\s+(\d+)\s+jobs/,
          logText
        );
        if (enrichJobsMatch?.[1]) {
          jobsProcessed = parseInt(enrichJobsMatch[1], 10);
        }

        // Try alternative patterns for jobs processed
        if (jobsProcessed === 0) {
          // Look for batch processing information
          batchMatch = RegExp.prototype.exec.call(
            /Processing batch of (\d+) jobs/,
            logText
          );
          if (batchMatch?.[1]) {
            jobsProcessed = parseInt(batchMatch[1], 10);
          }

          // Look for total jobs count
          totalMatch = RegExp.prototype.exec.call(
            /Total jobs to enrich: (\d+)/,
            logText
          );
          if (totalMatch?.[1]) {
            jobsProcessed = parseInt(totalMatch[1], 10);
          }

          // Look for job enrichment summary
          summaryMatch = RegExp.prototype.exec.call(
            /Job Enrichment processed (\d+) jobs/,
            logText
          );
          if (summaryMatch?.[1]) {
            jobsProcessed = parseInt(summaryMatch[1], 10);
          }
        }

        // Extract successful enrichments
        enrichedPattern =
          /Successfully enriched job|Added enrichment data for job/g;
        enrichedMatches = logText.match(enrichedPattern);
        jobsSucceeded = enrichedMatches ? enrichedMatches.length : 0;

        // Look for explicit success count
        enrichSuccessMatch = RegExp.prototype.exec.call(
          /(\d+) jobs enriched successfully/,
          logText
        );
        if (enrichSuccessMatch?.[1]) {
          jobsSucceeded = parseInt(enrichSuccessMatch[1], 10);
        }

        // Extract failures
        enrichFailPattern =
          /Failed to enrich job|Error enriching job|Skipping job/g;
        enrichFailMatches = logText.match(enrichFailPattern);
        if (enrichFailMatches) {
          jobsFailed = enrichFailMatches.length;
        } else {
          // Calculate failures if not explicitly found
          jobsFailed = jobsProcessed - jobsSucceeded;
        }

        // Look for explicit failure count
        enrichFailCountMatch = RegExp.prototype.exec.call(
          /(\d+) jobs failed to enrich/,
          logText
        );
        if (enrichFailCountMatch?.[1]) {
          jobsFailed = parseInt(enrichFailCountMatch[1], 10);
        }
        break;
      }

      case "parallelJobScraper": {
        // Extract jobs processed
        let scrapedJobsMatch;
        let batchMatch;
        let totalMatch;
        let completedMatch;
        let progressMatch;
        let savedPattern;
        let savedMatches;
        let scrapeSuccessMatch;
        let failedPattern;
        let failedMatches;
        let scrapeFailCountMatch;
        let duplicateMatch;

        scrapedJobsMatch = RegExp.prototype.exec.call(
          /Total jobs scraped:\s+(\d+)/,
          logText
        );
        if (scrapedJobsMatch?.[1]) {
          jobsProcessed = parseInt(scrapedJobsMatch[1], 10);
        }

        // Try alternative patterns for jobs processed
        if (jobsProcessed === 0) {
          // Look for batch processing information
          batchMatch = RegExp.prototype.exec.call(
            /Processing batch (\d+) with (\d+) jobs/,
            logText
          );
          if (batchMatch?.[2]) {
            jobsProcessed = parseInt(batchMatch[2], 10);
          }

          // Look for total jobs count
          totalMatch = RegExp.prototype.exec.call(
            /Found (\d+) jobs to scrape/,
            logText
          );
          if (totalMatch?.[1]) {
            jobsProcessed = parseInt(totalMatch[1], 10);
          }

          // Look for completed jobs count
          completedMatch = RegExp.prototype.exec.call(
            /Completed scraping (\d+) jobs/,
            logText
          );
          if (completedMatch?.[1]) {
            jobsProcessed = parseInt(completedMatch[1], 10);
          }

          // Look for progress information
          progressMatch = RegExp.prototype.exec.call(
            /Progress: (\d+)\/(\d+)/,
            logText
          );
          if (progressMatch?.[2]) {
            jobsProcessed = parseInt(progressMatch[2], 10);
          }
        }

        // Extract successful scrapes
        savedPattern =
          /Successfully saved job|Job saved to database|Added new job/g;
        savedMatches = logText.match(savedPattern);
        jobsSucceeded = savedMatches ? savedMatches.length : 0;

        // Look for explicit success count
        scrapeSuccessMatch = RegExp.prototype.exec.call(
          /(\d+) jobs scraped successfully/,
          logText
        );
        if (scrapeSuccessMatch?.[1]) {
          jobsSucceeded = parseInt(scrapeSuccessMatch[1], 10);
        }

        // Extract failures
        failedPattern =
          /Failed to save job|Error scraping job|Skipping job|Invalid job data/g;
        failedMatches = logText.match(failedPattern);
        if (failedMatches) {
          jobsFailed = failedMatches.length;
        }

        // Look for explicit failure count
        scrapeFailCountMatch = RegExp.prototype.exec.call(
          /(\d+) jobs failed to scrape/,
          logText
        );
        if (scrapeFailCountMatch?.[1]) {
          jobsFailed = parseInt(scrapeFailCountMatch[1], 10);
        }

        // Look for duplicate jobs count
        duplicateMatch = RegExp.prototype.exec.call(
          /(\d+) duplicate jobs skipped/,
          logText
        );
        if (duplicateMatch?.[1]) {
          // Add duplicates to failed count
          jobsFailed += parseInt(duplicateMatch[1], 10);
        }
        break;
      }

      default: {
        // For other job types, use generic patterns
        let jobStatsPattern;
        let jobStatsMatch;
        let processedPatterns;
        let successPatterns;
        let failurePatterns;
        let match;
        let valueIndex;

        // First, try to find standardized job stats in the logs
        // This is a more comprehensive pattern that looks for various formats
        jobStatsPattern =
          /(?:SUMMARY|STATS|REPORT).*?(?:Processed|Total).*?(\d+).*?(?:Success|Completed).*?(\d+).*?(?:Failed|Errors?).*?(\d+)/is;
        jobStatsMatch = RegExp.prototype.exec.call(jobStatsPattern, logText);
        if (jobStatsMatch && jobStatsMatch.length >= 4) {
          jobsProcessed = parseInt(jobStatsMatch[1], 10);
          jobsSucceeded = parseInt(jobStatsMatch[2], 10);
          jobsFailed = parseInt(jobStatsMatch[3], 10);
        } else {
          // If no standardized summary found, look for individual patterns

          // Look for any numbers in the logs that might indicate job counts
          processedPatterns = [
            /Processing\s+(\d+)/i,
            /Found\s+(\d+)/i,
            /Total:?\s+(\d+)/i,
            /Started processing (\d+)/i,
            /Batch of (\d+)/i,
            /(\d+) items to process/i,
            /(\d+) jobs? queued/i,
            /(\d+) tasks? scheduled/i,
          ];

          // Try each pattern until we find a match
          for (const pattern of processedPatterns) {
            match = RegExp.prototype.exec.call(pattern, logText);
            if (match?.[1]) {
              jobsProcessed = parseInt(match[1], 10);
              break;
            }
          }

          // Look for success indicators
          successPatterns = [
            /Success(fully)?:?\s+(\d+)/i,
            /Completed:?\s+(\d+)/i,
            /(\d+) jobs? completed/i,
            /(\d+) success(ful|fully)?/i,
            /(\d+) tasks? finished/i,
            /Processed (\d+) successfully/i,
          ];

          // Try each pattern until we find a match
          for (const pattern of successPatterns) {
            match = RegExp.prototype.exec.call(pattern, logText);
            if (match) {
              // The number could be in group 1 or 2 depending on the pattern
              valueIndex = match[2] !== undefined ? 2 : 1;
              if (match[valueIndex]) {
                jobsSucceeded = parseInt(match[valueIndex], 10);
                break;
              }
            }
          }

          // Look for failure indicators
          failurePatterns = [
            /Failed:?\s+(\d+)/i,
            /Errors?:?\s+(\d+)/i,
            /(\d+) jobs? failed/i,
            /(\d+) errors?/i,
            /(\d+) failures?/i,
            /(\d+) exceptions?/i,
            /(\d+) tasks? failed/i,
          ];

          // Try each pattern until we find a match
          for (const pattern of failurePatterns) {
            match = RegExp.prototype.exec.call(pattern, logText);
            if (match?.[1]) {
              jobsFailed = parseInt(match[1], 10);
              break;
            }
          }

          // If we found processed but not success/fail, assume all succeeded
          if (jobsProcessed > 0 && jobsSucceeded === 0 && jobsFailed === 0) {
            jobsSucceeded = jobsProcessed;
          }

          // If we found both succeeded and failed but not processed, calculate processed
          if (jobsProcessed === 0 && (jobsSucceeded > 0 || jobsFailed > 0)) {
            jobsProcessed = jobsSucceeded + jobsFailed;
          }
        }
        break;
      }
    }

    // Ensure we don't return negative values
    return {
      jobsProcessed: Math.max(0, jobsProcessed),
      jobsSucceeded: Math.max(0, jobsSucceeded),
      jobsFailed: Math.max(0, jobsFailed),
    };
  } catch (error) {
    logger.error(`Error extracting job stats for ${jobName}: ${error}`);
    return { jobsProcessed: 0, jobsSucceeded: 0, jobsFailed: 0 };
  }
}

// Track running jobs to prevent overlapping executions
const runningJobs: Record<JobType, boolean> = {
  parallelJobScraper: false,
  enrichJobDetails: false,
  scrapeJobDetails: false,
  checkExperienceRequirements: false,
  domainUpdate: false,
  updateJobMatches: false,
  dailySummary: false,
  marketAnalytics: false,
};

// Set maximum job durations (in milliseconds)
const MAX_JOB_DURATIONS: Record<JobType, number> = {
  parallelJobScraper: 30 * 24 * 60 * 60 * 1000, // 30 days (1 month)
  enrichJobDetails: 2 * 60 * 60 * 1000, // 2 hours
  scrapeJobDetails: 2 * 60 * 60 * 1000, // 2 hours
  checkExperienceRequirements: 1 * 60 * 60 * 1000, // 1 hour
  domainUpdate: 1 * 60 * 60 * 1000, // 1 hour
  updateJobMatches: 1 * 60 * 60 * 1000, // 1 hour
  dailySummary: 10 * 60 * 1000, // 10 minutes
  marketAnalytics: 3 * 60 * 60 * 1000, // 3 hours
};

// Helper function to execute a command with or without timeout
async function executeCommandStreaming(
  cwd: string,
  command: string,
  args: string[],
  jobName: JobType,
  maxDuration: number | null = null
): Promise<void> {
  // Set environment variables to indicate this is running from the scheduler
  const env = {
    ...process.env,
    RUNNING_FROM_SCHEDULER: "true",
  };
  // Add detailed logging for job start
  logger.info(
    `🔍 DIAGNOSTIC: Starting job ${jobName} with command: ${command} ${args.join(" ")}`
  );

  // Capture stdout and stderr for email notifications
  // Increase log capacity for better diagnostics
  let stdoutLogs: string[] = [];
  let stderrLogs: string[] = [];
  let lastOutputTime = Date.now();
  let isOutputStalled = false;

  // Check if job is already running
  if (runningJobs[jobName]) {
    logger.warn(
      `⚠️ Job ${jobName} is already running, skipping this execution`
    );

    // Send email notification about skipped job
    try {
      await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
        jobType: jobName,
        status: "Skipped",
        message: "Previous execution still running",
        timestamp: new Date().toISOString(),
      });
      logger.info(`✅ Email notification sent for skipped job: ${jobName}`);
    } catch (emailError) {
      logger.error(
        `❌ Failed to send email notification for skipped job: ${jobName}`,
        emailError
      );
    }

    return;
  }

  // Mark job as running
  runningJobs[jobName] = true;

  // Record start time for duration calculation
  const startTime = Date.now();
  let success = false;
  let failureReason = "";
  // Initialize job stats object that will be updated with actual values later
  let currentJobStats = { jobsProcessed: 0, jobsSucceeded: 0, jobsFailed: 0 };

  try {
    // Create a promise that resolves when the command completes
    const commandPromise = new Promise<void>((resolve, reject) => {
      logger.info(`🕐 Starting ${jobName} at ${new Date().toLocaleString()}`);

      logger.info(`🔄 About to execute command: ${command}`);
      const child = spawn(command, args, {
        cwd,
        env, // Use our custom env with RUNNING_FROM_SCHEDULER flag
        stdio: ["ignore", "pipe", "pipe"], // pipe stdout/stderr for logging
        shell: true, // if you need shell features
      });

      child.stdout.on("data", (chunk) => {
        const output = chunk.toString().trim();
        if (output) {
          lastOutputTime = Date.now();
          isOutputStalled = false;

          // Log with timestamp for better debugging
          const timestamp = new Date().toISOString();
          logger.info(`📤 [${timestamp}] ${jobName} stdout: ${output}`);

          // Store stdout for email notification (limit to last 50 lines)
          stdoutLogs.push(`[${new Date().toLocaleTimeString()}] ${output}`);
          if (stdoutLogs.length > 50) stdoutLogs.shift();
        }
      });

      child.stderr.on("data", (chunk) => {
        const output = chunk.toString().trim();
        if (output) {
          lastOutputTime = Date.now();
          isOutputStalled = false;

          // Log with timestamp for better debugging
          const timestamp = new Date().toISOString();
          logger.warn(`⚠️ [${timestamp}] ${jobName} stderr: ${output}`);

          // Store stderr for email notification (limit to last 50 lines)
          stderrLogs.push(`[${new Date().toLocaleTimeString()}] ${output}`);
          if (stderrLogs.length > 50) stderrLogs.shift();
        }
      });

      // Check for stalled output every minute
      const stallCheckInterval = setInterval(() => {
        const timeSinceLastOutput = Date.now() - lastOutputTime;
        // If no output for 5 minutes, log a warning
        if (timeSinceLastOutput > 5 * 60 * 1000 && !isOutputStalled) {
          isOutputStalled = true;
          const warningMsg = `⚠️ No output from ${jobName} for 5 minutes`;
          logger.warn(warningMsg);
          stderrLogs.push(`[${new Date().toLocaleTimeString()}] ${warningMsg}`);
        }
      }, 60 * 1000);

      child.on("error", (err) => {
        logger.error(`Failed to start ${jobName}: ${err.message}`);
        reject(err);
      });

      child.on("close", (code, signal) => {
        // Clear the stall check interval
        clearInterval(stallCheckInterval);

        if (signal) {
          const msg = `${jobName} was killed by signal ${signal}`;
          logger.error(msg);
          stderrLogs.push(`[${new Date().toLocaleTimeString()}] ${msg}`);
          return reject(new Error(msg));
        }
        if (code !== 0) {
          const msg = `${jobName} exited with code ${code}`;
          logger.error(msg);
          stderrLogs.push(`[${new Date().toLocaleTimeString()}] ${msg}`);
          return reject(new Error(msg));
        }
        logger.info(`✅ ${jobName} completed successfully`);
        stdoutLogs.push(
          `[${new Date().toLocaleTimeString()}] ✅ Job completed successfully`
        );
        resolve();
      });
    });
    // If maxDuration is provided, create a timeout promise and race against it
    if (maxDuration !== null) {
      // Create a timeout promise
      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(() => {
          reject(
            new Error(
              `${jobName} timed out after ${maxDuration / (60 * 1000)} minutes`
            )
          );
        }, maxDuration);
      });

      // Race the command against the timeout
      await Promise.race([commandPromise, timeoutPromise]);
    } else {
      // No timeout, just wait for the command to complete
      logger.info(
        `⏳ ${jobName} is running without a timeout - will wait until completion`
      );
      await commandPromise;
    }

    // Calculate duration
    const endTime = Date.now();
    const durationMs = endTime - startTime;
    const durationMinutes = Math.floor(durationMs / (60 * 1000));
    const durationHours = Math.floor(durationMinutes / 60);
    const durationDays = Math.floor(durationHours / 24);

    let durationText = "";
    if (durationDays > 0) {
      durationText = `${durationDays} days, ${durationHours % 24} hours, ${durationMinutes % 60} minutes`;
    } else if (durationHours > 0) {
      durationText = `${durationHours} hours, ${durationMinutes % 60} minutes`;
    } else {
      durationText = `${durationMinutes} minutes`;
    }

    logger.info(`✅ ${jobName} completed successfully after ${durationText}`);

    // Set success flag to true since we completed without errors
    success = true;

    // Extract job statistics based on job type
    currentJobStats = extractJobStats(jobName, stdoutLogs);

    // Create report data for email
    const reportDate = new Date().toLocaleDateString();
    const reportTitle = `${jobName} Processing Report`;

    // Format success rate
    const successRate =
      currentJobStats.jobsProcessed > 0
        ? `${((currentJobStats.jobsSucceeded / currentJobStats.jobsProcessed) * 100).toFixed(1)}%`
        : "N/A";

    // Log the job statistics
    logger.info(`📊 Job Statistics for ${jobName}:`);
    logger.info(`  • Processed: ${currentJobStats.jobsProcessed}`);
    logger.info(`  • Succeeded: ${currentJobStats.jobsSucceeded}`);
    logger.info(`  • Failed: ${currentJobStats.jobsFailed}`);
    logger.info(`  • Success Rate: ${successRate}`);

    // Create job types array for email template
    const jobTypes = [
      {
        type: jobName,
        count: currentJobStats.jobsProcessed,
        success: currentJobStats.jobsSucceeded,
        failure: currentJobStats.jobsFailed,
      },
    ];

    // Send email notification about job completion with real data
    try {
      await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
        jobType: jobName,
        status: "Completed",
        timestamp: new Date().toISOString(),
        duration: durationText,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),

        // Add job statistics
        jobsProcessed: currentJobStats.jobsProcessed,
        jobsSucceeded: currentJobStats.jobsSucceeded,
        jobsFailed: currentJobStats.jobsFailed,
        successRate: successRate,

        // Add formatted data for email template
        reportTitle: reportTitle,
        reportDate: reportDate,
        processingTime: durationText,
        jobTypes: jobTypes,

        // Add stdout and stderr logs to email (limited to prevent huge emails)
        stdoutLogs:
          stdoutLogs.length > 0
            ? stdoutLogs.join("\n").substring(0, 5000) // Limit to 5K chars
            : "No output logs available",
        stderrLogs:
          stderrLogs.length > 0
            ? stderrLogs.join("\n").substring(0, 5000) // Limit to 5K chars
            : "No error logs available",

        // Add command for reference
        command: `${command} ${args.join(" ")}`,

        // Add system info for better diagnostics
        systemInfo: {
          nodeVersion: process.version,
          platform: process.platform,
          memory: process.memoryUsage(),
          uptime: process.uptime(),
        },

        // Add details for better reporting
        details: {
          totalProcessed: currentJobStats.jobsProcessed,
          totalSucceeded: currentJobStats.jobsSucceeded,
          totalFailed: currentJobStats.jobsFailed,
          successRate: successRate,
        },
      });
      logger.info(`✅ Email notification sent for job completion: ${jobName}`);
    } catch (emailError) {
      logger.error(
        `❌ Failed to send email notification for job completion: ${jobName}`,
        emailError
      );
    }
  } catch (error) {
    // Calculate duration even for failed jobs
    const endTime = Date.now();
    const durationMs = endTime - startTime;
    const durationMinutes = Math.floor(durationMs / (60 * 1000));
    const durationHours = Math.floor(durationMinutes / 60);
    const durationDays = Math.floor(durationHours / 24);

    let durationText = "";
    if (durationDays > 0) {
      durationText = `${durationDays} days, ${durationHours % 24} hours, ${durationMinutes % 60} minutes`;
    } else if (durationHours > 0) {
      durationText = `${durationHours} hours, ${durationMinutes % 60} minutes`;
    } else {
      durationText = `${durationMinutes} minutes`;
    }

    logger.error(`❌ ${jobName} failed after ${durationText}:`, error);

    // Set success flag to false since we had an error
    success = false;
    failureReason = error instanceof Error ? error.message : String(error);

    // Extract job statistics based on job type
    currentJobStats = extractJobStats(jobName, stdoutLogs);

    // Format success rate
    const successRate =
      currentJobStats.jobsProcessed > 0
        ? `${((currentJobStats.jobsSucceeded / currentJobStats.jobsProcessed) * 100).toFixed(1)}%`
        : "N/A";

    // Log the error and job statistics
    logger.error(`❌ Job Statistics for ${jobName} (FAILED):`);
    logger.error(`  • Error: ${failureReason}`);
    logger.error(`  • Processed: ${currentJobStats.jobsProcessed}`);
    logger.error(`  • Succeeded: ${currentJobStats.jobsSucceeded}`);
    logger.error(`  • Failed: ${currentJobStats.jobsFailed}`);
    logger.error(`  • Success Rate: ${successRate}`);

    // Create failure reasons array for email template
    const failureReasons = [
      {
        reason: failureReason,
        count: 1,
      },
    ];

    // Send detailed email notification about job failure with real data
    try {
      await sendEmailNotification(EmailNotificationType.CRON_ERROR, {
        jobType: jobName,
        status: "Failed",
        errorTitle: `Cron Job Failed: ${jobName}`,
        errorMessage: failureReason,
        errorStack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        duration: durationText,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),

        // Add job statistics
        jobsProcessed: currentJobStats.jobsProcessed,
        jobsSucceeded: currentJobStats.jobsSucceeded,
        jobsFailed: currentJobStats.jobsFailed,
        successRate: successRate,

        // Add formatted data for email template
        reportTitle: `${jobName} Failure Report`,
        reportDate: new Date().toLocaleDateString(),
        processingTime: durationText,
        failureReasons: failureReasons,

        // Add stdout and stderr logs to email (limited to prevent huge emails)
        stdoutLogs:
          stdoutLogs.length > 0
            ? stdoutLogs.join("\n").substring(0, 5000) // Limit to 5K chars
            : "No output logs available",
        stderrLogs:
          stderrLogs.length > 0
            ? stderrLogs.join("\n").substring(0, 5000) // Limit to 5K chars
            : "No error logs available",

        // Add command for reference
        command: `${command} ${args.join(" ")}`,

        // Add system info for better diagnostics
        systemInfo: {
          nodeVersion: process.version,
          platform: process.platform,
          memory: process.memoryUsage(),
          uptime: process.uptime(),
        },

        // Add details for better reporting
        details: {
          totalProcessed: currentJobStats.jobsProcessed,
          totalSucceeded: currentJobStats.jobsSucceeded,
          totalFailed: currentJobStats.jobsFailed,
          successRate: successRate,
          errorDetails: failureReason,
        },
      });
      logger.info(
        `✅ Error notification email sent for job failure: ${jobName}`
      );
    } catch (emailError) {
      logger.error(
        `❌ Failed to send error notification email for job failure: ${jobName}`,
        emailError
      );
    }
  } finally {
    // Calculate final duration
    const endTime = Date.now();
    const durationMs = endTime - startTime;

    // Track this job in the global stats with the correct success value
    globalJobStats.trackJob(jobName, success, durationMs, failureReason);

    // Log final status to console
    if (success) {
      logger.info(`✅ ${jobName} completed and tracked successfully`);
    } else {
      logger.error(`❌ ${jobName} failed and error tracked`);
    }

    // Log to standardized format for easier parsing by other tools
    logger.jobStats({
      jobType: jobName,
      processed: currentJobStats.jobsProcessed,
      succeeded: currentJobStats.jobsSucceeded,
      failed: currentJobStats.jobsFailed,
      duration: durationMs,
      details: {
        success: success,
        failureReason: failureReason || undefined,
        command: `${command} ${args.join(" ")}`,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
      },
    });

    // Mark job as no longer running, regardless of outcome
    runningJobs[jobName] = false;
  }
}

// Run the parallel job scraper every day at 1 AM with adaptive resource management
const parallelJobScraperJob = new CronJob(
  "0 1 * * *", // Every day at 1 AM
  async () => {
    logger.info(
      "🕒 Running scheduled parallel job scraper with adaptive resource management"
    );

    // Use the shared circuit breaker to check if we should run the job
    await withCircuitBreaker("parallelJobScraper", async () => {
      // Run the parallel job scraper with adaptive resource management
      await executeCommandStreaming(
        process.cwd(),
        "npm",
        ["run", "parallel-adaptive", "--max-old-space-size=2048"],
        "parallelJobScraper",
        MAX_JOB_DURATIONS.parallelJobScraper
      );
    });
  },
  null,
  false,
  "America/New_York"
);

// Run the job enrichment process every day at 4 AM
const enrichJobDetailsJob = new CronJob(
  "0 4 * * *", // Every day at 4 AM
  async () => {
    logger.info("🕒 Running scheduled job enrichment");

    // Use the shared circuit breaker to check if we should run the job
    await withCircuitBreaker("enrichJobDetails", async () => {
      await executeCommandStreaming(
        process.cwd(),
        "npm",
        ["run", "enrich"],
        "enrichJobDetails",
        MAX_JOB_DURATIONS.enrichJobDetails
      );
    });
  },
  null,
  false, // Don't start automatically, we'll start it explicitly
  "America/New_York"
);

// Run the job details scraper every day at 6 AM
const scrapeJobDetailsJob = new CronJob(
  "0 6 * * *", // Every day at 6 AM
  async () => {
    logger.info(
      "🕒 Running scheduled job details scraper with garbage collection enabled"
    );

    // Use the shared circuit breaker to check if we should run the job
    await withCircuitBreaker("scrapeJobDetails", async () => {
      await executeCommandStreaming(
        process.cwd(),
        "npm",
        ["run", "details-gc"], // Use the TypeScript version with garbage collection enabled
        "scrapeJobDetails",
        MAX_JOB_DURATIONS.scrapeJobDetails
      );
    });
  },
  null,
  false, // Don't start automatically, we'll start it explicitly
  "America/New_York"
);

// Run the experience requirements checker every day at 8 AM
const checkExperienceRequirementsJob = new CronJob(
  "0 8 * * *", // Every day at 8 AM
  async () => {
    logger.info("🕒 Running scheduled experience requirements checker");

    // Use the shared circuit breaker to check if we should run the job
    await withCircuitBreaker("checkExperienceRequirements", async () => {
      await executeCommandStreaming(
        process.cwd(),
        "npm",
        ["run", "check-exp"],
        "checkExperienceRequirements",
        MAX_JOB_DURATIONS.checkExperienceRequirements
      );
    });
  },
  null,
  false, // Don't start automatically, we'll start it explicitly
  "America/New_York"
);

// Run the domain update job every day at 10 AM
const domainUpdateJob = new CronJob(
  "0 10 * * *", // Every day at 10 AM
  async () => {
    logger.info("🕒 Running scheduled company domain update");

    // Use the shared circuit breaker to check if we should run the job
    await withCircuitBreaker("domainUpdate", async () => {
      await executeCommandStreaming(
        process.cwd(),
        "npm",
        ["run", "update-domains"],
        "domainUpdate",
        MAX_JOB_DURATIONS.domainUpdate
      );
    });
  },
  null,
  false, // Don't start automatically, we'll start it explicitly
  "America/New_York"
);

// Run the job matches update every day at 12 PM
const updateJobMatchesJob = new CronJob(
  "0 12 * * *", // Every day at 12 PM
  async () => {
    logger.info("🕒 Running scheduled job matches update");

    // Use the shared circuit breaker to check if we should run the job
    await withCircuitBreaker("updateJobMatches", async () => {
      await executeCommandStreaming(
        process.cwd(),
        "npm",
        ["run", "update-matches"],
        "updateJobMatches",
        MAX_JOB_DURATIONS.updateJobMatches
      );
    });
  },
  null,
  false, // Don't start automatically, we'll start it explicitly
  "America/New_York"
);

// Run the daily summary job every day at 11 PM
const dailySummaryJob = new CronJob(
  "0 23 * * *", // Every day at 11 PM
  async () => {
    logger.info("📊 Running scheduled daily job summary");

    // Use the shared circuit breaker to check if we should run the job
    await withCircuitBreaker("dailySummary", async () => {
      await executeCommandStreaming(
        process.cwd(),
        "npm",
        ["run", "daily-summary"],
        "dailySummary",
        MAX_JOB_DURATIONS.dailySummary
      );
    });
  },
  null,
  false, // Don't start automatically, we'll start it explicitly
  "America/New_York"
);

// Run the market analytics collector every Sunday at 2 AM
const marketAnalyticsJob = new CronJob(
  "0 2 * * 0", // Every Sunday at 2 AM
  async () => {
    logger.info("📊 Running scheduled market analytics collection");

    // Use the shared circuit breaker to check if we should run the job
    await withCircuitBreaker("marketAnalytics", async () => {
      await executeCommandStreaming(
        process.cwd(),
        "npm",
        ["run", "market-analytics"],
        "marketAnalytics",
        MAX_JOB_DURATIONS.marketAnalytics
      );
    });
  },
  null,
  false, // Don't start automatically, we'll start it explicitly
  "America/New_York"
);

// Reset stats at midnight
const resetStatsJob = new CronJob(
  "0 0 * * *", // Every day at midnight
  () => {
    logger.info("🔄 Resetting daily job statistics");
    globalJobStats.resetDailyStats();
  },
  null,
  false,
  "America/New_York"
);

// Circuit breaker auto-reset is now handled internally by the circuit breaker implementation
// The separate cron job has been removed to avoid process spawning overhead

// Format function for human-readable dates
function formatNextDate(nextDate: any): string {
  try {
    // Convert to string first to ensure we have a parseable format
    const dateStr = nextDate.toString();
    // Parse the date string
    const date = new Date(dateStr);
    return date.toLocaleString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
      timeZone: "America/New_York",
    });
  } catch (e) {
    logger.warn(`Failed to format date: ${e}`);
    return String(nextDate); // Return original if parsing fails
  }
}

// Start all scheduled jobs
export function startScheduledJobs() {
  logger.info("🚀 Starting scheduled jobs");

  // Log the current time
  const now = new Date();
  logger.info(
    `Current time: ${now.toLocaleString("en-US", { timeZone: "America/New_York" })} (America/New_York)`
  );
  logger.info(`UTC time: ${now.toISOString()}`);

  // Start all the cron jobs
  parallelJobScraperJob.start();
  enrichJobDetailsJob.start();
  scrapeJobDetailsJob.start();
  checkExperienceRequirementsJob.start();
  domainUpdateJob.start();
  updateJobMatchesJob.start();
  dailySummaryJob.start();
  marketAnalyticsJob.start();
  resetStatsJob.start();

  // Log next run times in a human-readable format
  logger.info(`📅 Next job runs:`);
  logger.info(
    `  • Parallel Job Scraper: ${formatNextDate(parallelJobScraperJob.nextDate())}`
  );
  logger.info(
    `  • Job Enrichment: ${formatNextDate(enrichJobDetailsJob.nextDate())}`
  );
  logger.info(
    `  • Job Details Scraper: ${formatNextDate(scrapeJobDetailsJob.nextDate())}`
  );
  logger.info(
    `  • Experience Requirements: ${formatNextDate(checkExperienceRequirementsJob.nextDate())}`
  );
  logger.info(
    `  • Domain Update: ${formatNextDate(domainUpdateJob.nextDate())}`
  );
  logger.info(
    `  • Job Matches Update: ${formatNextDate(updateJobMatchesJob.nextDate())}`
  );
  logger.info(
    `  • Daily Summary: ${formatNextDate(dailySummaryJob.nextDate())}`
  );
  logger.info(
    `  • Market Analytics: ${formatNextDate(marketAnalyticsJob.nextDate())}`
  );
  // Circuit breaker auto-reset is now handled internally

  logger.info("✅ Scheduled jobs started");
}

// Stop all scheduled jobs
export function stopScheduledJobs() {
  logger.info("🛑 Stopping scheduled jobs");

  // Stop all the cron jobs
  parallelJobScraperJob.stop();
  enrichJobDetailsJob.stop();
  scrapeJobDetailsJob.stop();
  checkExperienceRequirementsJob.stop();
  domainUpdateJob.stop();
  updateJobMatchesJob.stop();
  dailySummaryJob.stop();
  marketAnalyticsJob.stop();

  logger.info("✅ Scheduled jobs stopped");
}

// Note: Jobs are started from index.ts, not here

#!/usr/bin/env node

/**
 * Simple debug script to check basic cron job issues
 *
 * This script uses minimal dependencies to check:
 * 1. Redis connectivity
 * 2. Basic system info
 * 3. Running processes
 */

// CommonJS script - require is already available

async function simpleDebug() {
  console.log("🔍 Starting simple cron job debug");

  try {
    // Check if Redis is available
    console.log("📡 Testing Redis connectivity...");
    try {
      // Try to import and test Redis
      const { redis } = await import("../../workers/redis.js");
      await redis.ping();
      console.log("✅ Redis is connected and responding");

      // Check for running jobs
      console.log("🏃 Checking for running jobs...");
      const runningJobs = await redis.hgetall("cron:running_jobs");
      console.log(`📋 Running jobs: ${JSON.stringify(runningJobs, null, 2)}`);

      // Check worker health
      console.log("🏥 Checking worker health...");
      const healthData = await redis.hgetall("worker:health");
      console.log(`📋 Health entries: ${Object.keys(healthData).length}`);

      // Check circuit breaker data
      console.log("🧠 Checking circuit breaker data...");
      const circuitData = await redis.hgetall("worker:circuit");
      console.log(`📋 Circuit entries: ${Object.keys(circuitData).length}`);

      // Show some details
      for (const [key, value] of Object.entries(circuitData)) {
        try {
          const circuit = JSON.parse(value);
          console.log(
            `🔧 ${key}: ${circuit.state} (failures: ${circuit.failureCount || 0})`
          );
        } catch (e) {
          console.log(`🔧 ${key}: Invalid data`);
        }
      }
    } catch (error) {
      console.error("❌ Redis test failed:", error.message);
    }

    // Check system info
    console.log("📊 System information:");
    console.log(`🖥️ Node version: ${process.version}`);
    console.log(`🖥️ Platform: ${process.platform}`);
    console.log(`🖥️ Architecture: ${process.arch}`);
    console.log(`🖥️ PID: ${process.pid}`);
    console.log(`🖥️ Uptime: ${Math.floor(process.uptime())} seconds`);

    // Check memory usage
    const memUsage = process.memoryUsage();
    console.log(`💾 Memory usage:`);
    console.log(`  RSS: ${Math.round(memUsage.rss / 1024 / 1024)} MB`);
    console.log(
      `  Heap Used: ${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`
    );
    console.log(
      `  Heap Total: ${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`
    );

    // Check environment variables
    console.log("🌍 Environment check:");
    console.log(`NODE_ENV: ${process.env.NODE_ENV || "not set"}`);
    console.log(`REDIS_URL: ${process.env.REDIS_URL ? "set" : "not set"}`);
    console.log(
      `DATABASE_URL: ${process.env.DATABASE_URL ? "set" : "not set"}`
    );

    // Check current working directory
    console.log(`📁 Current directory: ${process.cwd()}`);

    // Try to check if cron jobs are actually scheduled
    console.log("⏰ Checking if we can access cron job files...");
    const fs = require("fs");
    const path = require("path");

    try {
      const cronJobsPath = path.join(process.cwd(), "cron", "jobs");
      const files = fs.readdirSync(cronJobsPath);
      console.log(`📋 Cron job files found: ${files.length}`);
      files.forEach((file) => {
        if (file.endsWith(".ts") || file.endsWith(".js")) {
          console.log(`  📄 ${file}`);
        }
      });
    } catch (error) {
      console.error("❌ Could not read cron jobs directory:", error.message);
    }

    console.log("🎉 Simple debug completed!");
  } catch (error) {
    console.error("❌ Simple debug failed:", error);
  }
}

async function main() {
  try {
    await simpleDebug();
    process.exit(0);
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

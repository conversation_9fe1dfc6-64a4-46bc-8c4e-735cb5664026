@echo off
echo 🔍 Starting Redis for local development...

REM Check if Docker is available
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed or not in PATH
    echo Please install Docker Desktop to run Redis locally
    pause
    exit /b 1
)

echo ✅ Docker is available

REM Stop any existing Redis container
echo 🛑 Stopping any existing Redis container...
docker stop redis-local >nul 2>&1
docker rm redis-local >nul 2>&1

REM Start Redis container
echo 🚀 Starting Redis container...
docker run -d ^
  --name redis-local ^
  -p 6379:6379 ^
  redis:7-alpine ^
  redis-server --appendonly yes

if %errorlevel% neq 0 (
    echo ❌ Failed to start Redis container
    pause
    exit /b 1
)

echo ✅ Redis container started successfully

REM Wait a moment for Redis to be ready
echo ⏳ Waiting for Redis to be ready...
timeout /t 3 /nobreak >nul

REM Test Redis connection
echo 🔍 Testing Redis connection...
docker exec redis-local redis-cli ping >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Redis is not responding
    pause
    exit /b 1
)

echo ✅ Redis is running and responding to ping
echo 📋 Redis is available at: localhost:6379
echo 🔧 You can now run the cron job debug scripts

REM Show Redis logs
echo.
echo 📊 Redis container logs:
docker logs redis-local --tail 10

echo.
echo 🎉 Redis setup complete!
echo 💡 To stop Redis later, run: docker stop redis-local
echo 💡 To view Redis logs, run: docker logs redis-local -f
pause

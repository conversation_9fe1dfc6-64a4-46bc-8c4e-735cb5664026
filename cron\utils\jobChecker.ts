// cron/utils/jobChecker.ts
// Utility to check if any jobs are currently running

import { PrismaClient } from "@prisma/client";
import { logger } from "./logger";

// Create a singleton Prisma client
let prismaClient: PrismaClient | null = null;

/**
 * Get a Prisma client instance with error handling
 * @returns PrismaClient A Prisma client instance
 */
function getPrismaClient(): PrismaClient {
  try {
    if (!prismaClient) {
      prismaClient = new PrismaClient({
        log: [
          { level: "warn", emit: "event" },
          { level: "error", emit: "event" },
        ],
      });

      // Add event listeners for logging
      prismaClient.$on("warn", (e) => {
        logger.warn(`Prisma warning: ${e.message}`);
      });

      prismaClient.$on("error", (e) => {
        logger.error(`Prisma error: ${e.message}`);
      });

      logger.info("✅ Prisma client initialized successfully");
    }
    return prismaClient;
  } catch (error) {
    logger.error(`Failed to initialize Prisma client: ${error}`);
    // Create a minimal client that won't throw errors when methods are called
    return {
      jobStats: {
        count: async () => 0,
      },
    } as unknown as PrismaClient;
  }
}

/**
 * Check if any jobs are currently running
 * @returns Promise<boolean> True if jobs are running, false otherwise
 */
export async function areJobsRunning(): Promise<boolean> {
  try {
    const prisma = getPrismaClient();

    // Verify that jobStats exists before calling count
    if (
      !prisma ||
      !prisma.jobStats ||
      typeof prisma.jobStats.count !== "function"
    ) {
      logger.error("Prisma client or jobStats model not properly initialized");
      return false; // Changed to false to avoid blocking operations when we can't check
    }

    // Query the database for running jobs
    const runningJobs = await prisma.jobStats.count({
      where: {
        endTime: null,
      },
    });

    return runningJobs > 0;
  } catch (error) {
    logger.error(`Error checking for running jobs: ${error}`);
    // If we can't check, return false to avoid blocking operations
    return false;
  }
}

/**
 * Get the count of currently running jobs
 * @returns Promise<number> The number of running jobs
 */
export async function getRunningJobCount(): Promise<number> {
  try {
    const prisma = getPrismaClient();

    // Verify that jobStats exists before calling count
    if (
      !prisma ||
      !prisma.jobStats ||
      typeof prisma.jobStats.count !== "function"
    ) {
      logger.error("Prisma client or jobStats model not properly initialized");
      return 0; // Return 0 instead of -1 to avoid potential undefined errors
    }

    // Query the database for running jobs
    const runningJobs = await prisma.jobStats.count({
      where: {
        endTime: null,
      },
    });

    return runningJobs;
  } catch (error) {
    logger.error(`Error getting running job count: ${error}`);
    // If we can't check, return 0 to avoid potential undefined errors
    return 0;
  }
}

/**
 * Close the Prisma client connection
 */
export async function closePrismaConnection(): Promise<void> {
  if (prismaClient) {
    try {
      await prismaClient.$disconnect();
      prismaClient = null;
      logger.info("✅ Prisma client disconnected successfully");
    } catch (error) {
      logger.error(`Error disconnecting Prisma client: ${error}`);
    }
  }
}

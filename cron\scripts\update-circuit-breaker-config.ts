// cron/scripts/update-circuit-breaker-config.ts
// Script to update circuit breaker configuration and test its effectiveness

import { logger } from "../utils/logger";
import os from "os";
import { PrismaClient } from "@prisma/client";
import { ImprovedImprovedCircuitBreaker, CircuitState } from "../utils/improvedImprovedCircuitBreaker";
import { sendEmailNotification, EmailNotificationType } from "../utils/emailService";

const prisma = new PrismaClient();

// Function to format bytes to human-readable format
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Function to get detailed system resource information
function getSystemResourceInfo() {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;
  
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const loadPerCpu = loadAvg / cpuCount;
  const cpuUsagePercent = loadPerCpu * 100;
  
  return {
    memory: {
      total: formatBytes(totalMemory),
      free: formatBytes(freeMemory),
      used: formatBytes(usedMemory),
      usagePercent: memoryUsagePercent.toFixed(2) + "%"
    },
    cpu: {
      count: cpuCount,
      loadAverage: loadAvg.toFixed(2),
      loadPerCpu: loadPerCpu.toFixed(2),
      usagePercent: cpuUsagePercent.toFixed(2) + "%"
    }
  };
}

// Function to force garbage collection
function forceGarbageCollection() {
  if (global.gc) {
    logger.info("🧹 Forcing garbage collection...");
    global.gc();
    return true;
  } else {
    logger.warn("⚠️ Garbage collection not available. Run with --expose-gc flag.");
    return false;
  }
}

// Function to update circuit breaker configuration
async function updateCircuitBreakerConfig() {
  logger.info("🔄 Updating circuit breaker configuration...");
  
  // Get system resource information before update
  const beforeResources = getSystemResourceInfo();
  logger.info(`📊 System resources before update: ${JSON.stringify(beforeResources, null, 2)}`);
  
  // Try to free up memory
  forceGarbageCollection();
  
  // Create a new circuit breaker instance with updated configuration
  const circuitBreaker = new ImprovedImprovedCircuitBreaker({
    memoryThresholdPercent: 80, // Threshold for opening circuit
    cpuThresholdPercent: 80, // Threshold for opening circuit
    errorThresholdCount: 3, // Number of errors before opening circuit
    resetTimeoutMs: 120000, // Wait 2 minutes before testing if system has recovered
    checkIntervalMs: 10000, // Check system resources every 10 seconds
    consecutiveReadingsForOpen: 2, // Number of consecutive readings above threshold to open circuit
    consecutiveReadingsForClose: 3, // Number of consecutive readings below threshold to close circuit
    consecutiveReadingsForDegraded: 1, // Number of consecutive readings for degraded state
    degradedMemoryThresholdPercent: 60, // Memory threshold for degraded mode
    degradedCpuThresholdPercent: 60, // CPU threshold for degraded mode
    onStateChange: (oldState, newState) => {
      logger.info(`🔄 Circuit breaker state changed from ${oldState} to ${newState}`);
    }
  });
  
  // Test the circuit breaker
  logger.info("🧪 Testing circuit breaker...");
  
  // Get current system resources
  const { memoryUsage, cpuUsage } = circuitBreaker["checkSystemResources"]();
  
  logger.info(`🧠 Current resource usage - Memory: ${memoryUsage.toFixed(2)}%, CPU: ${cpuUsage.toFixed(2)}%`);
  
  // Check if circuit breaker would open with current resource usage
  if (memoryUsage >= 80 || cpuUsage >= 80) {
    logger.warn(`⚠️ Circuit breaker would open with current resource usage`);
    
    // Try to free up memory again
    forceGarbageCollection();
    
    // Check resources after GC
    const postGCResources = getSystemResourceInfo();
    logger.info(`📊 System resources after GC: ${JSON.stringify(postGCResources, null, 2)}`);
    
    // Send notification about high resource usage
    try {
      await sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
        timestamp: new Date().toISOString(),
        message: "High system resource usage detected during circuit breaker configuration update",
        memoryUsage: postGCResources.memory.usagePercent,
        cpuUsage: postGCResources.cpu.usagePercent
      });
      
      logger.info(`✅ Sent resource usage notification email`);
    } catch (error) {
      logger.error(`❌ Error sending notification: ${error}`);
    }
  }
  
  // Save the configuration to the database for future reference
  try {
    await prisma.systemConfig.upsert({
      where: { key: 'circuitBreakerConfig' },
      update: {
        value: JSON.stringify({
          memoryThresholdPercent: 80,
          cpuThresholdPercent: 80,
          errorThresholdCount: 3,
          resetTimeoutMs: 120000,
          checkIntervalMs: 10000,
          consecutiveReadingsForOpen: 2,
          consecutiveReadingsForClose: 3,
          consecutiveReadingsForDegraded: 1,
          degradedMemoryThresholdPercent: 60,
          degradedCpuThresholdPercent: 60,
          updatedAt: new Date().toISOString()
        })
      },
      create: {
        key: 'circuitBreakerConfig',
        value: JSON.stringify({
          memoryThresholdPercent: 80,
          cpuThresholdPercent: 80,
          errorThresholdCount: 3,
          resetTimeoutMs: 120000,
          checkIntervalMs: 10000,
          consecutiveReadingsForOpen: 2,
          consecutiveReadingsForClose: 3,
          consecutiveReadingsForDegraded: 1,
          degradedMemoryThresholdPercent: 60,
          degradedCpuThresholdPercent: 60,
          updatedAt: new Date().toISOString()
        })
      }
    });
    
    logger.info(`✅ Saved circuit breaker configuration to database`);
  } catch (error) {
    logger.error(`❌ Error saving configuration to database: ${error}`);
  }
  
  logger.info("✅ Circuit breaker configuration updated successfully");
}

// Run the update if this file is executed directly
if (require.main === module) {
  updateCircuitBreakerConfig()
    .then(() => {
      logger.info("✅ Circuit breaker configuration update completed");
      process.exit(0);
    })
    .catch(error => {
      logger.error(`❌ Error updating circuit breaker configuration: ${error}`);
      process.exit(1);
    });
}

export { updateCircuitBreakerConfig };

// cron/jobs/scrapeJobDetails.ts

import { logger } from "../utils/logger";
import { chromium, devices } from "playwright";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import dotenv from "dotenv";
import { getPrismaClient } from "../utils/prismaClient";
import { config } from "../config";
import Redis from "ioredis";
import { withJobLock } from "../utils/redisJobLock";
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";

// Load environment variables
dotenv.config();

// We'll initialize the Prisma client in the scrapeJobDetails function
let prisma: any;

// Create Redis client for locking
if (!config.redis.url) {
  throw new Error("Redis URL is not defined in config.redis.url");
}
const redis = new Redis(config.redis.url);

// Get configuration values
const MAX_JOBS_TO_PROCESS = config.jobs.jobDetailsScraper.maxJobsPerRun;

// Disable file storage to save disk space and memory
// We'll use in-memory storage instead
const inMemoryCookieStorage = new Map<string, any[]>();

// List of common user agents to rotate
const USER_AGENTS = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
];

// List of common device profiles to rotate
const DEVICE_PROFILES = [
  devices["Desktop Chrome"],
  devices["Desktop Firefox"],
  devices["Desktop Safari"],
  devices["Desktop Edge"],
];

/**
 * Get a random user agent from the list
 */
function getRandomUserAgent(): string {
  return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

/**
 * Get a random device profile from the list
 */
function getRandomDeviceProfile(): any {
  return DEVICE_PROFILES[Math.floor(Math.random() * DEVICE_PROFILES.length)];
}

/**
 * Generate a random delay between min and max milliseconds
 */
function getRandomDelay(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Extract domain from URL
 */
function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    logger.warn(`Failed to extract domain from URL "${url}": ${error}`);
    return "";
  }
}

/**
 * Load cookies for a specific domain from in-memory storage
 */
function loadCookiesForDomain(domain: string): any[] {
  return inMemoryCookieStorage.get(domain) || [];
}

/**
 * Save cookies for a specific domain to in-memory storage
 */
function saveCookiesForDomain(domain: string, cookies: any[]): void {
  inMemoryCookieStorage.set(domain, cookies);

  // Limit the size of the in-memory storage to prevent memory leaks
  if (inMemoryCookieStorage.size > 100) {
    // If we have too many domains, remove the oldest ones
    const keys = Array.from(inMemoryCookieStorage.keys());
    const keysToRemove = keys.slice(0, keys.length - 50); // Keep only the 50 most recent

    for (const key of keysToRemove) {
      inMemoryCookieStorage.delete(key);
    }

    logger.info(
      `🧹 Cleaned up in-memory cookie storage, removed ${keysToRemove.length} old domains`
    );
  }
}

/**
 * Check if a page contains CAPTCHA or verification challenge
 */
async function detectVerificationChallenge(page: any): Promise<boolean> {
  try {
    // Check for common CAPTCHA and verification elements
    const challengeIndicators = [
      // Text content checks
      "captcha",
      "robot",
      "human verification",
      "security check",
      "prove you are human",
      "verify your identity",
      "additional verification",
      "please enable javascript",
      "please enable cookies",

      // Element selectors
      'iframe[src*="captcha"]',
      'iframe[src*="recaptcha"]',
      'iframe[src*="hcaptcha"]',
      "div.g-recaptcha",
      "div.h-captcha",
      'form[action*="captcha"]',
      'input[name*="captcha"]',
      'img[src*="captcha"]',
    ];

    // Check for text indicators in page content
    const pageContent = await page.content();
    const lowerContent = pageContent.toLowerCase();

    for (const indicator of challengeIndicators) {
      if (
        typeof indicator === "string" &&
        lowerContent.includes(indicator.toLowerCase())
      ) {
        return true;
      }
    }

    // Check for element selectors
    for (const selector of challengeIndicators) {
      if (
        selector.includes("[") ||
        selector.includes(".") ||
        selector.includes("#")
      ) {
        const elements = await page.$$(selector);
        if (elements.length > 0) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    logger.warn(`Error detecting verification challenge: ${error}`);
    return false;
  }
}

/**
 * Attempt to bypass simple verification challenges
 */
async function attemptChallengeBypass(page: any): Promise<boolean> {
  try {
    // Try common bypass techniques

    // 1. Click on "I'm not a robot" checkbox if present
    const robotCheckbox = await page.$("div.recaptcha-checkbox-border");
    if (robotCheckbox) {
      await robotCheckbox.click();
      await page.waitForTimeout(getRandomDelay(1000, 2000));
      return true;
    }

    // 2. Try to dismiss cookie banners which might be blocking content
    // Common cookie accept button selectors
    const cookieSelectors = [
      'button[id*="cookie"][id*="accept"]',
      'button[class*="cookie"][class*="accept"]',
      'a[id*="cookie"][id*="accept"]',
      'a[class*="cookie"][class*="accept"]',
      'button:has-text("Accept")',
      'button:has-text("Accept All")',
      'button:has-text("I Accept")',
      'button:has-text("Allow")',
      'button:has-text("Allow All")',
      'button:has-text("Continue")',
      'button:has-text("Agree")',
    ];

    for (const selector of cookieSelectors) {
      const cookieButton = await page.$(selector);
      if (cookieButton) {
        await cookieButton.click();
        await page.waitForTimeout(getRandomDelay(500, 1000));
        return true;
      }
    }

    // 3. Try to click "Continue" or "Proceed" buttons
    const continueSelectors = [
      'button:has-text("Continue")',
      'button:has-text("Proceed")',
      'button:has-text("Next")',
      'button:has-text("Submit")',
      'input[type="submit"]',
    ];

    for (const selector of continueSelectors) {
      const continueButton = await page.$(selector);
      if (continueButton) {
        await continueButton.click();
        await page.waitForTimeout(getRandomDelay(500, 1000));
        return true;
      }
    }

    return false;
  } catch (error) {
    logger.warn(`Error attempting challenge bypass: ${error}`);
    return false;
  }
}

/**
 * Scrape detailed information for job listings
 */
/**
 * Get memory usage in MB
 */
function getMemoryUsageMB() {
  const memoryUsage = process.memoryUsage();
  return {
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024),
  };
}

async function scrapeJobDetails() {
  const startTime = new Date();
  logger.info(`🔍 Starting job details scraper at ${startTime.toISOString()}`);
  try {
    // Initialize Prisma client
    prisma = await getPrismaClient("web");

    // Find jobs that need details scraped (jobs without descriptions)
    const allJobs = await prisma.jobListing.findMany({
      where: {
        isActive: true,
        description: { equals: "" }, // Empty string instead of null
        url: { not: "" }, // Not empty string instead of not null
      },
      select: {
        id: true,
        url: true,
        platform: true, // Using platform instead of source
        title: true,
        company: true,
      },
      take: MAX_JOBS_TO_PROCESS,
    });

    if (allJobs.length === 0) {
      logger.info("No jobs found that need details scraped");
      return;
    }

    logger.info(`Found ${allJobs.length} jobs that need details scraped`);

    // Process in smaller batches to manage memory
    const BATCH_SIZE = 5; // Process 5 jobs at a time
    const batches = [];

    for (let i = 0; i < allJobs.length; i += BATCH_SIZE) {
      batches.push(allJobs.slice(i, i + BATCH_SIZE));
    }

    logger.info(
      `Split jobs into ${batches.length} batches of ${BATCH_SIZE} jobs each`
    );

    // Launch browser with stealth mode
    const browser = await chromium.launch({
      headless: true,
      args: [
        "--disable-blink-features=AutomationControlled",
        "--disable-features=IsolateOrigins,site-per-process",
        "--disable-site-isolation-trials",
        "--disable-web-security",
        "--disable-features=BlockInsecurePrivateNetworkRequests",
        "--no-sandbox",
        "--disable-setuid-sandbox",
      ],
    });

    let successCount = 0;
    let failureCount = 0;

    // Process each batch
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      logger.info(
        `Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} jobs`
      );

      // Process each job in the batch
      for (const job of batch) {
        try {
          logger.info(
            `Processing job: ${job.id} - ${job.title} at ${job.company}`
          );

          // Extract domain for cookie management
          const domain = extractDomain(job.url);

          // Get cookies for this domain if they exist
          const cookies = loadCookiesForDomain(domain);

          // Select a random device profile and user agent
          const deviceProfile = getRandomDeviceProfile();
          const userAgent = getRandomUserAgent();

          // Create a new context with anti-bot measures - create a fresh context for each job
          const context = await browser.newContext({
            ...deviceProfile,
            userAgent,
            bypassCSP: true, // Bypass Content Security Policy
            javaScriptEnabled: true,
            locale: "en-US",
            timezoneId: "America/Los_Angeles",
            permissions: ["geolocation", "notifications"],
            viewport: {
              width: 1920 + Math.floor(Math.random() * 100),
              height: 1080 + Math.floor(Math.random() * 100),
            },
            // Add stored cookies if available
            cookies: cookies,
          });

          // Add browser fingerprinting evasion
          await context.addInitScript(() => {
            // Override navigator properties to make detection harder
            Object.defineProperty(navigator, "webdriver", { get: () => false });
            Object.defineProperty(navigator, "plugins", {
              get: () => [1, 2, 3, 4, 5],
            });
            Object.defineProperty(navigator, "languages", {
              get: () => ["en-US", "en"],
            });

            // Override permissions API
            if (navigator.permissions) {
              // @ts-ignore - Ignoring TypeScript errors for this browser fingerprinting code
              navigator.permissions.query = function () {
                return Promise.resolve({ state: "granted", onchange: null });
              };
            }

            // Add fake canvas fingerprint - use a safer approach
            try {
              // Use a simpler approach to avoid TypeScript errors
              // @ts-ignore - We need to ignore TypeScript for browser fingerprinting
              HTMLCanvasElement.prototype.getContext = function () {
                // @ts-ignore
                const originalGetContext =
                  this.constructor.prototype.getContext;
                // @ts-ignore
                const context = originalGetContext.call(this, ...arguments);

                if (context && arguments[0] === "2d") {
                  // Add a property to make fingerprinting harder
                  // @ts-ignore
                  Object.getPrototypeOf(context).toString = function () {
                    return "[object CanvasRenderingContext2D]";
                  };

                  // Add random noise to toDataURL
                  // @ts-ignore
                  context.canvas.toDataURL = function () {
                    // @ts-ignore
                    const originalToDataURL =
                      this.constructor.prototype.toDataURL;
                    // @ts-ignore
                    return originalToDataURL.call(this, ...arguments);
                  };
                }
                return context;
              };
            } catch (e) {
              // Ignore errors in fingerprinting evasion
              console.log("Error in fingerprinting evasion:", e);
            }
          });

          const page = await context.newPage();

          // Add a random delay before navigation to appear more human-like
          await page.waitForTimeout(getRandomDelay(500, 2000));

          // Navigate to the URL
          await page.goto(job.url, {
            waitUntil: "domcontentloaded",
            timeout: getRandomDelay(10000, 20000), // Randomized timeout
          });

          // Add another random delay after navigation
          await page.waitForTimeout(getRandomDelay(1000, 3000));

          // Check for verification challenges
          const hasChallenge = await detectVerificationChallenge(page);

          if (hasChallenge) {
            logger.info(
              `🔍 Detected verification challenge for job ${job.id}, attempting bypass...`
            );

            // Try to bypass the challenge
            const bypassSuccessful = await attemptChallengeBypass(page);

            if (bypassSuccessful) {
              logger.info(
                `✅ Successfully bypassed verification for job ${job.id}`
              );

              // Wait for content to load after bypass
              await page.waitForTimeout(getRandomDelay(2000, 4000));
            } else {
              logger.warn(`⚠️ Failed to bypass verification for job ${job.id}`);
            }
          }

          // Save cookies for future use
          const currentCookies = await context.cookies();
          if (currentCookies.length > 0) {
            saveCookiesForDomain(domain, currentCookies);
          }

          // Skip human-like scrolling for now to avoid __name errors

          // Simple scroll to bottom of page
          await page.evaluate("window.scrollTo(0, document.body.scrollHeight)");

          // Wait for content to load with a randomized delay
          await page.waitForTimeout(getRandomDelay(1000, 2500));

          // Extract job details using a more robust approach
          let jobDescription = "";
          let location = "";
          let salary = "";
          let employmentType = "";

          try {
            // Extract the body text without waiting for specific selectors
            const bodyText = await page.evaluate<string>(
              'document.body.innerText || document.body.textContent || ""'
            );

            // Use a simpler approach to extract content to avoid __name errors
            const mainContent = await page.evaluate<string>(() => {
              // Try to find job description containers
              const jobDesc =
                document.querySelector(".job-description") ||
                document.querySelector("#job-description") ||
                document.querySelector('[data-automation="jobDescription"]');

              if (jobDesc) {
                return (
                  ((jobDesc as HTMLElement).innerText || jobDesc.textContent) ??
                  ""
                );
              }

              // Try to find article or main content
              const article =
                document.querySelector("article") ||
                document.querySelector("main") ||
                document.querySelector(".description") ||
                document.querySelector(".content");

              if (article) {
                return (article.innerText || article.textContent) ?? "";
              }

              // Fallback to body content
              return document.body.innerText ?? document.body.textContent ?? "";
            });

            // Use the main content if available, otherwise fall back to body text
            jobDescription = mainContent || bodyText;

            // Check for remote work first
            const remotePatterns = [
              /\bremote\b/i,
              /\bwork from home\b/i,
              /\bwfh\b/i,
              /\bvirtual\b/i,
              /\btelework\b/i,
              /\bremotely\b/i,
            ];

            let isRemote = false;

            // Check if job is remote
            for (const pattern of remotePatterns) {
              if (pattern.exec(jobDescription) || pattern.exec(bodyText)) {
                isRemote = true;
                break;
              }
            }

            // If remote, leave location empty
            if (isRemote) {
              location = "";
            } else {
              // Try to find city names (proper nouns followed by optional state code)
              const cityPatterns = [
                /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),\s*([A-Z]{2})\b/, // City, ST format
                /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b(?:\s+area)?\b/, // Just city name
              ];

              // First try in the job description
              for (const pattern of cityPatterns) {
                const match = pattern.exec(jobDescription);
                if (match) {
                  if (match[2]) {
                    location = `${match[1]}, ${match[2]}`;
                  } else {
                    location = match[1];
                  }
                  break;
                }
              }

              // If not found, try in the full body text
              if (!location) {
                for (const pattern of cityPatterns) {
                  const match = pattern.exec(bodyText);
                  if (match) {
                    if (match[2]) {
                      location = `${match[1]}, ${match[2]}`;
                    } else {
                      location = match[1];
                    }
                    break;
                  }
                }
              }
            }

            // Define salary type enum
            const SALARY_TYPES = {
              HOURLY: "hourly",
              YEARLY: "yearly",
              MONTHLY: "monthly",
              WEEKLY: "weekly",
              DAILY: "daily",
            };

            // Variables to store min and max salary and type
            let salaryMin: number | null = null;
            let salaryMax: number | null = null;
            let salaryType: string | null = null;

            // Function to convert salary text to number
            const extractSalaryNumber = (text: string): number => {
              // Remove $ and commas
              let numStr = text.replace(/[$,]/g, "");

              // Handle 'k' suffix (thousands)
              if (numStr.toLowerCase().endsWith("k")) {
                numStr = numStr.slice(0, -1);
                return parseFloat(numStr) * 1000;
              }

              return parseFloat(numStr);
            };

            // Patterns for salary ranges
            const salaryRangePatterns = [
              // Dollar amount ranges
              /\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*(?:to|-|–)\s*\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i,
              /(\$\d+(?:[,.]\d+)?)\s*(?:-|to|–)\s*(\$\d+(?:[,.]\d+)?)/i,

              // Numeric ranges (without $ sign)
              /(\d{2,3}(?:,\d{3})*(?:\.\d{2})?)\s*(?:to|-|–)\s*(\d{2,3}(?:,\d{3})*(?:\.\d{2})?)/i,

              // K notation
              /\$(\d+(?:\.\d+)?k)\s*(?:to|-|–)\s*\$(\d+(?:\.\d+)?k)/i,
              /(\d+(?:\.\d+)?k)\s*(?:to|-|–)\s*(\d+(?:\.\d+)?k)/i,
            ];

            // Patterns for single salary with type
            const salarySinglePatterns = [
              // Dollar amount with type
              /\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*(?:per|\/|an)\s*(hr|hour|yr|year|mo|month|wk|week|day)/i,
              /(\$\d+(?:[,.]\d+)?)\s*(?:per|\/|an)\s*(hr|hour|yr|year|mo|month|wk|week|day)/i,

              // Numeric with type (without $ sign)
              /(\d{2,3}(?:,\d{3})*(?:\.\d{2})?)\s*(?:per|\/|an)\s*(hr|hour|yr|year|mo|month|wk|week|day)/i,

              // K notation with type
              /\$(\d+(?:\.\d+)?k)\s*(?:per|\/|an)\s*(hr|hour|yr|year|mo|month|wk|week|day)/i,
              /(\d+(?:\.\d+)?k)\s*(?:per|\/|an)\s*(hr|hour|yr|year|mo|month|wk|week|day)/i,
            ];

            // First try to find salary ranges in job description
            let foundSalary = false;

            // Check for salary ranges in job description
            for (const pattern of salaryRangePatterns) {
              const match = pattern.exec(jobDescription);
              if (match?.[1] && match?.[2]) {
                salaryMin = extractSalaryNumber(match[1]);
                salaryMax = extractSalaryNumber(match[2]);
                foundSalary = true;

                // Look for salary type nearby
                const typePatterns = [
                  /per\s+(hr|hour|yr|year|mo|month|wk|week|day)/i,
                  /\/(hr|hour|yr|year|mo|month|wk|week|day)/i,
                  /\b(hourly|yearly|monthly|weekly|daily)\b/i,
                ];

                for (const typePattern of typePatterns) {
                  const typeMatch = typePattern.exec(jobDescription);
                  if (typeMatch?.[1]) {
                    const typeText = typeMatch[1].toLowerCase();
                    if (typeText.includes("hr") || typeText === "hourly") {
                      salaryType = SALARY_TYPES.HOURLY;
                    } else if (
                      typeText.includes("yr") ||
                      typeText === "yearly"
                    ) {
                      salaryType = SALARY_TYPES.YEARLY;
                    } else if (
                      typeText.includes("mo") ||
                      typeText === "monthly"
                    ) {
                      salaryType = SALARY_TYPES.MONTHLY;
                    } else if (
                      typeText.includes("wk") ||
                      typeText === "weekly"
                    ) {
                      salaryType = SALARY_TYPES.WEEKLY;
                    } else if (
                      typeText.includes("day") ||
                      typeText === "daily"
                    ) {
                      salaryType = SALARY_TYPES.DAILY;
                    }
                    break;
                  }
                }

                // Default to yearly if no type found
                salaryType ??= SALARY_TYPES.YEARLY;

                break;
              }
            }

            // If no range found, check for single salary with type
            if (!foundSalary) {
              for (const pattern of salarySinglePatterns) {
                const match = pattern.exec(jobDescription);
                if (match?.[1] && match?.[2]) {
                  const amount = extractSalaryNumber(match[1]);
                  salaryMin = amount;
                  salaryMax = amount;
                  const typeText = match[2].toLowerCase();
                  if (typeText.includes("hr") || typeText === "hour") {
                    salaryType = SALARY_TYPES.HOURLY;
                  } else if (typeText.includes("yr") || typeText === "year") {
                    salaryType = SALARY_TYPES.YEARLY;
                  } else if (typeText.includes("mo") || typeText === "month") {
                    salaryType = SALARY_TYPES.MONTHLY;
                  } else if (typeText.includes("wk") || typeText === "week") {
                    salaryType = SALARY_TYPES.WEEKLY;
                  } else if (typeText.includes("day")) {
                    salaryType = SALARY_TYPES.DAILY;
                  }

                  break;
                }
              }
            }

            // If still not found, try in the full body text
            if (!foundSalary) {
              // Check for salary ranges in body text
              for (const pattern of salaryRangePatterns) {
                const match = pattern.exec(bodyText);
                if (match?.[1] && match?.[2]) {
                  salaryMin = extractSalaryNumber(match[1]);
                  salaryMax = extractSalaryNumber(match[2]);

                  // Default to yearly if no type found
                  salaryType = SALARY_TYPES.YEARLY;

                  break;
                }
              }

              // If still no range found, check for single salary with type
              for (const pattern of salarySinglePatterns) {
                const match = pattern.exec(bodyText);
                if (match?.[1] && match?.[2]) {
                  const amount = extractSalaryNumber(match[1]);
                  salaryMin = amount;
                  salaryMax = amount;

                  const typeText = match[2].toLowerCase();
                  if (typeText.includes("hr") || typeText === "hour") {
                    salaryType = SALARY_TYPES.HOURLY;
                  } else if (typeText.includes("yr") || typeText === "year") {
                    salaryType = SALARY_TYPES.YEARLY;
                  } else if (typeText.includes("mo") || typeText === "month") {
                    salaryType = SALARY_TYPES.MONTHLY;
                  } else if (typeText.includes("wk") || typeText === "week") {
                    salaryType = SALARY_TYPES.WEEKLY;
                  } else if (typeText.includes("day")) {
                    salaryType = SALARY_TYPES.DAILY;
                  }

                  break;
                }
              }
            }

            // Format the salary string
            if (salaryMin !== null) {
              if (salaryMax !== null && salaryMax !== salaryMin) {
                salary = `${salaryMin}-${salaryMax}`;
              } else {
                salary = `${salaryMin}`;
              }

              if (salaryType) {
                salary += ` ${salaryType}`;
              }
            }

            // Define employment type enum values
            const EMPLOYMENT_TYPES = {
              FULL_TIME: "Full-time",
              PART_TIME: "Part-time",
              CONTRACT: "Contract",
              TEMPORARY: "Temporary",
              PERMANENT: "Permanent",
              FREELANCE: "Freelance",
              INTERNSHIP: "Internship",
            };

            // Map of patterns to employment type enum values
            const employmentTypePatternMap = [
              {
                pattern: /\b(?:full[- ]?time|ft)\b/i,
                type: EMPLOYMENT_TYPES.FULL_TIME,
              },
              {
                pattern: /\b(?:part[- ]?time|pt)\b/i,
                type: EMPLOYMENT_TYPES.PART_TIME,
              },
              {
                pattern: /\b(?:contract|contractor)\b/i,
                type: EMPLOYMENT_TYPES.CONTRACT,
              },
              {
                pattern: /\b(?:temp|temporary)\b/i,
                type: EMPLOYMENT_TYPES.TEMPORARY,
              },
              {
                pattern: /\b(?:permanent|perm)\b/i,
                type: EMPLOYMENT_TYPES.PERMANENT,
              },
              {
                pattern: /\b(?:freelance|freelancer)\b/i,
                type: EMPLOYMENT_TYPES.FREELANCE,
              },
              {
                pattern: /\b(?:intern|internship)\b/i,
                type: EMPLOYMENT_TYPES.INTERNSHIP,
              },
            ];

            // First try in the job description
            for (const { pattern, type } of employmentTypePatternMap) {
              if (pattern.exec(jobDescription)) {
                employmentType = type;
                break;
              }
            }

            // If not found, try in the full body text
            if (!employmentType) {
              for (const { pattern, type } of employmentTypePatternMap) {
                if (pattern.exec(bodyText)) {
                  employmentType = type;
                  break;
                }
              }
            }

            // If still not found, check for employment type labels
            if (!employmentType) {
              const labelPatterns = [
                /(?:Employment|Job) Type\s*:?\s*([^,\n]+)/i,
                /(?:Position|Role) Type\s*:?\s*([^,\n]+)/i,
              ];

              // Check in job description
              for (const pattern of labelPatterns) {
                const match = pattern.exec(jobDescription);
                if (match?.[1]) {
                  const typeText = match[1].trim().toLowerCase();

                  // Map the extracted text to our enum values
                  if (typeText.includes("full") && typeText.includes("time")) {
                    employmentType = EMPLOYMENT_TYPES.FULL_TIME;
                  } else if (
                    typeText.includes("part") &&
                    typeText.includes("time")
                  ) {
                    employmentType = EMPLOYMENT_TYPES.PART_TIME;
                  } else if (typeText.includes("contract")) {
                    employmentType = EMPLOYMENT_TYPES.CONTRACT;
                  } else if (typeText.includes("temp")) {
                    employmentType = EMPLOYMENT_TYPES.TEMPORARY;
                  } else if (typeText.includes("perm")) {
                    employmentType = EMPLOYMENT_TYPES.PERMANENT;
                  } else if (typeText.includes("free")) {
                    employmentType = EMPLOYMENT_TYPES.FREELANCE;
                  } else if (typeText.includes("intern")) {
                    employmentType = EMPLOYMENT_TYPES.INTERNSHIP;
                  }

                  if (employmentType) break;
                }
              }
            }

            // If we still don't have a good description, try platform-specific approaches
            if (jobDescription.length < 100) {
              if (job.platform?.toLowerCase().includes("linkedin")) {
                const linkedinDesc = await page
                  .locator(".description__text")
                  .textContent({ timeout: 5000 });
                if (linkedinDesc) jobDescription = linkedinDesc;
              } else if (job.platform?.toLowerCase().includes("indeed")) {
                // Try Indeed-specific selectors and handle errors
                try {
                  const indeedDesc = await page
                    .locator("#jobDescriptionText")
                    .textContent({ timeout: 5000 });
                  if (indeedDesc) jobDescription = indeedDesc;
                } catch (e) {
                  logger.debug(`Indeed description extraction failed: ${e}`);
                }
              }
            }
          } catch (extractionError) {
            const typedExtractionError = extractionError as any;
            logger.warn(
              `⚠️ Error extracting details for job ${job.id}: ${typedExtractionError.message ?? String(extractionError)}`
            );
            // Continue with whatever content we have
          }

          // Data validation and cleanup

          // Validate and clean job description
          jobDescription = jobDescription.trim();

          // Check if description contains verification or CAPTCHA indicators
          const verificationIndicators = [
            "additional verification required",
            "waiting for",
            "to respond",
            "enable javascript and cookies",
            "captcha",
            "robot check",
            "security check",
            "verify you are a human",
            "access to this page has been denied",
            "please enable cookies",
            "404",
            "not found",
            "page you requested",
            "sorry",
            "challenge",
            "cloudflare",
            "firewall",
            "blocked",
            "suspicious activity",
            "unusual traffic",
            "ddos protection",
            "checking your browser",
            "checking if the site connection is secure",
            "just a moment",
            "one more step",
            "please wait",
            "please complete the security check",
            "please complete this check",
            "please confirm you are a human",
            "please verify you are a human",
          ];

          // Check for verification issues in the page content
          const hasVerificationIssue = verificationIndicators.some(
            (indicator) => jobDescription.toLowerCase().includes(indicator)
          );

          // If description has verification issues, try one more bypass attempt
          if (hasVerificationIssue || jobDescription.length < 100) {
            logger.warn(
              `⚠️ Job ${job.id} has verification issues or insufficient description, attempting final bypass...`
            );

            // Try a more aggressive bypass approach
            try {
              // Try to click any buttons that might help bypass verification
              const bypassSelectors = [
                'button:has-text("Continue")',
                'button:has-text("I am human")',
                'button:has-text("Verify")',
                'button:has-text("Submit")',
                'button:has-text("Start")',
                'button:has-text("Next")',
                'button:has-text("Proceed")',
                'input[type="submit"]',
                ".recaptcha-checkbox",
                "#recaptcha-anchor",
                ".g-recaptcha",
                ".h-captcha",
                'iframe[src*="captcha"]',
              ];

              // Try each selector
              for (const selector of bypassSelectors) {
                let elements: any[] = [];
                try {
                  elements = await page.$$(selector);
                } catch {
                  elements = [];
                }
                if (elements.length > 0) {
                  // Try to click the element, but don't fail if it doesn't work
                  await elements[0].click().catch(() => {
                    logger.debug(
                      `Failed to click ${selector} in bypass attempt`
                    );
                  });
                  logger.info(
                    `Attempted to click ${selector} in final bypass attempt`
                  );
                  await page.waitForTimeout(getRandomDelay(2000, 4000));
                }
              }

              // Try to refresh the page
              await page.reload({
                waitUntil: "domcontentloaded",
                timeout: 10000,
              });
              await page.waitForTimeout(getRandomDelay(2000, 4000));

              // Try to extract content again after bypass attempt
              const newBodyText = await page.evaluate(() => {
                return (
                  document.body.innerText ?? document.body.textContent ?? ""
                );
              });

              // Check if we now have better content
              if (newBodyText.length > jobDescription.length) {
                jobDescription = newBodyText;
                logger.info(
                  `✅ Final bypass attempt improved content for job ${job.id}`
                );
              }

              // Check again for verification issues
              const stillHasVerificationIssue = verificationIndicators.some(
                (indicator) => jobDescription.toLowerCase().includes(indicator)
              );

              if (stillHasVerificationIssue || jobDescription.length < 100) {
                throw new Error(
                  "Verification required or insufficient content after bypass attempts"
                );
              }
            } catch (bypassError) {
              // Log the bypass error and throw a more specific error
              logger.warn(
                `Bypass attempt failed: ${bypassError instanceof Error ? bypassError.message : String(bypassError)}`
              );
              throw new Error(
                "Verification required or insufficient content after bypass attempts"
              );
            }
          }

          // Validate and clean location
          location = location.trim();

          // Check if location contains verification indicators or is too short
          if (
            verificationIndicators.some((indicator) =>
              location.toLowerCase().includes(indicator)
            ) ||
            location.length < 2
          ) {
            // Reset location to empty string if invalid
            location = "";
          }

          // Validate city names (should start with capital letter and be a reasonable length)
          if (location) {
            // Check if location starts with a capital letter
            if (!/^[A-Z]/.test(location)) {
              location = "";
            }
            // Check if location is too long (likely not a real location)
            else if (location.length > 50) {
              location = "";
            }
            // Check if location contains job title or other non-location text
            else if (
              /advocate|advisor|nurse|therapist|counselor|intelligence|analyst|easy apply/i.test(
                location
              )
            ) {
              location = "";
            }
            // Extract just the city and state if it contains other text
            else {
              // Try to extract city, state format
              const cityStatePattern = /([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/;
              const cityStateMatch = cityStatePattern.exec(location);
              if (cityStateMatch) {
                location = `${cityStateMatch[1]}, ${cityStateMatch[2]}`;
              }
            }
          }

          // Validate and clean salary
          salary = salary.trim();

          // Ensure salary is in the correct format (number + type or range + type)
          const validSalaryPattern =
            /^\d+(?:-\d+)?\s+(?:hourly|yearly|monthly|weekly|daily)$/;
          if (salary && !validSalaryPattern.test(salary)) {
            // Try to fix common salary format issues
            if (/^\d+$/.test(salary)) {
              // Just a number, assume yearly
              salary = `${salary} yearly`;
            } else if (/^\d+-\d+$/.test(salary)) {
              // Just a range, assume yearly
              salary = `${salary} yearly`;
            } else {
              // Invalid format, reset
              salary = "";
            }
          }

          // Validate and clean employment type
          employmentType = employmentType.trim();

          // Ensure employment type is one of our valid types
          const validEmploymentTypes = [
            "Full-time",
            "Part-time",
            "Contract",
            "Temporary",
            "Permanent",
            "Freelance",
            "Internship",
          ];
          if (
            employmentType &&
            !validEmploymentTypes.includes(employmentType)
          ) {
            // Invalid employment type, reset
            employmentType = "";
          }

          // Update the job in the database
          await prisma.jobListing.update({
            where: { id: job.id },
            data: {
              description: jobDescription || undefined,
              location: location || undefined,
              salary: salary || undefined,
              employmentType: employmentType || undefined,
              lastCheckedAt: new Date(), // Using lastCheckedAt instead of lastScraped
            },
          });

          // Close the context to free resources
          await context.close();

          logger.info(`✅ Successfully scraped details for job: ${job.id}`);
          successCount++;

          // Add a small delay between requests to avoid rate limiting
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 + Math.random() * 2000)
          );

          // Clean up variables to help with memory management
          // Set variables to null to help garbage collection
          // (Using global scope to avoid redeclaration errors)
        } catch (error) {
          const typedJobError = error as any;
          logger.error(`❌ Error scraping details for job ${job.id}:`, error);
          failureCount++;

          // Determine the type of failure
          const errorMessage = typedJobError.message ?? String(error);
          let failureReason = "Unknown error";

          if (
            errorMessage.includes("Verification required") ||
            errorMessage.includes("CAPTCHA") ||
            errorMessage.includes("verification")
          ) {
            failureReason = "Verification required";
          } else if (errorMessage.includes("timeout")) {
            failureReason = "Timeout";
          } else if (errorMessage.includes("navigation")) {
            failureReason = "Navigation failed";
          } else if (errorMessage.includes("insufficient content")) {
            failureReason = "Insufficient content";
          }

          // Mark the job as having been checked (even though it failed)
          await prisma.jobListing.update({
            where: { id: job.id },
            data: {
              // Set a placeholder description with the failure reason
              description: `Failed to scrape job details: ${failureReason}`,
              // Clear any partial data that might have been extracted
              location: "",
              salary: "",
              employmentType: "",
              lastCheckedAt: new Date(),
            },
          });
        }
      }

      // Add a delay between batches to allow natural GC to occur
      if (batchIndex < batches.length - 1) {
        logger.info(
          `Batch ${batchIndex + 1} completed. Pausing before next batch...`
        );
        await new Promise((resolve) => setTimeout(resolve, 3000));

        // Log memory usage between batches
        const memoryUsageMB = getMemoryUsageMB();
        logger.info(
          `Memory usage between batches: RSS: ${memoryUsageMB.rss}MB, Heap: ${memoryUsageMB.heapUsed}MB / ${memoryUsageMB.heapTotal}MB`
        );
      }
    }

    // Close the browser
    await browser.close();

    const endTime = new Date();
    const durationMs = endTime.getTime() - startTime.getTime();
    const durationSec = durationMs / 1000;

    // Get memory usage for reporting
    const memoryUsageMB = getMemoryUsageMB();

    // Log job statistics in standardized format for easier parsing
    logger.jobStats({
      jobType: "scrapeJobDetails",
      processed: allJobs.length,
      succeeded: successCount,
      failed: failureCount,
      duration: durationMs,
      details: {
        batchCount: batches.length,
        batchSize: BATCH_SIZE,
        maxJobsToProcess: MAX_JOBS_TO_PROCESS,
        verificationChallenges: allJobs.length - successCount - failureCount,
        memory: {
          rss: memoryUsageMB.rss,
          heapUsed: memoryUsageMB.heapUsed,
          heapTotal: memoryUsageMB.heapTotal,
        },
      },
    });

    logger.info(`🏁 Job details scraper completed at ${endTime.toISOString()}`);
    logger.info(
      `📊 Summary: ${successCount} jobs successfully scraped, ${failureCount} jobs failed`
    );
    logger.info(`⏱️ Total duration: ${durationSec.toFixed(2)} seconds`);
    logger.info(
      `💾 Memory usage: RSS: ${memoryUsageMB.rss}MB, Heap: ${memoryUsageMB.heapUsed}MB / ${memoryUsageMB.heapTotal}MB`
    );

    // Force garbage collection if available
    try {
      if (typeof global.gc === "function") {
        global.gc();
        logger.info("Forced final garbage collection");
      } else {
        // If gc is not available, log it but continue
        logger.info(
          "Final garbage collection not available in this environment"
        );
      }
    } catch (gcError) {
      logger.warn("Failed to force garbage collection", gcError);
    }

    // Send email notification with results
    await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
      jobType: "Job Details Scraper",
      status: "Completed",
      startTime,
      endTime,
      duration: `${durationSec.toFixed(2)} seconds`,
      jobsProcessed: allJobs.length,
      jobsSucceeded: successCount,
      jobsFailed: failureCount,
      command: "npm run details",
      // Include memory usage information
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        memory: memoryUsageMB,
        uptime: process.uptime(),
      },
    });
  } catch (error) {
    // Type assertion for TypeScript
    const typedError = error as any;
    logger.error("❌ Error in job details scraper:", error);

    // Get memory usage for error reporting
    const memoryUsageMB = getMemoryUsageMB();
    const endTime = new Date();
    const durationMs = endTime.getTime() - startTime.getTime();

    // Log error with standardized job statistics
    logger.jobStats({
      jobType: "scrapeJobDetails",
      processed: 0,
      succeeded: 0,
      failed: 1, // The job itself failed
      duration: durationMs,
      details: {
        error: typedError.message ?? String(error),
        errorStack: typedError.stack,
        memory: {
          rss: memoryUsageMB.rss,
          heapUsed: memoryUsageMB.heapUsed,
          heapTotal: memoryUsageMB.heapTotal,
        },
      },
    });

    // Send error notification with memory usage information
    await sendEmailNotification(EmailNotificationType.ERROR_ALERT, {
      jobType: "Job Details Scraper",
      status: "Failed",
      startTime,
      endTime,
      error: typedError.message ?? String(error),
      stack: typedError.stack ?? "No stack trace available",
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        memory: memoryUsageMB,
        uptime: process.uptime(),
      },
    });
  } finally {
    // Clean up resources
    try {
      // Close Redis connection
      await redis.quit().catch((err: Error) => {
        logger.error("Error closing Redis connection:", err);
      });

      // Disconnect Prisma
      await prisma.$disconnect().catch((err: Error) => {
        logger.error("Error disconnecting Prisma:", err);
      });

      // Force garbage collection if available
      if (typeof global.gc === "function") {
        global.gc();
        logger.info("Forced final garbage collection");
      }
    } catch (cleanupError) {
      logger.error("❌ Error during cleanup:", cleanupError);
    }
  }
}

/**
 * Main function to run the job with proper cleanup
 */
async function main() {
  try {
    // Check circuit breaker state first
    const circuitBreakerState = await getCircuitBreakerState();
    if (circuitBreakerState === CircuitState.OPEN) {
      logger.error("❌ Circuit breaker is OPEN, skipping job execution");
      return 1;
    }

    // Use the Redis-based job lock
    const result = await withJobLock(
      "job_details_scraper",
      // Use a default value if lockTimeoutSeconds is not defined in config
      (config.jobs.jobDetailsScraper as any).lockTimeoutSeconds ?? 600, // 600 seconds default
      scrapeJobDetails
    );

    // If the job was skipped due to lock, log it
    if (result === null) {
      logger.warn("🔒 Job details scraper was skipped due to existing lock");
    }

    logger.info("✅ Job details scraper script completed");
    return 0;
  } catch (error) {
    logger.error("❌ Job details scraper script failed:", error);
    return 1;
  }
}

/**
 * Get the current circuit breaker state from Redis
 */
async function getCircuitBreakerState(): Promise<CircuitState> {
  try {
    const state = await redis.get("circuit_breaker:state");
    return (state as CircuitState) || CircuitState.CLOSED;
  } catch (error) {
    logger.error(
      `❌ Error getting circuit breaker state: ${error instanceof Error ? error.message : String(error)}`
    );
    return CircuitState.CLOSED; // Default to CLOSED if we can't get the state
  }
}

// Run the scraper with proper cleanup
main()
  .then((exitCode) => {
    process.exit(exitCode);
  })
  .catch((error) => {
    console.error("Unhandled error in main:", error);
    process.exit(1);
  });

// cron/jobs/fastJobScraper.ts

import { logger } from "../utils/logger";
import { PrismaClient } from "@prisma/client";
import { saveJobsToDatabase } from "../lib/saveJobsToDatabase.js";
import { cleanupOldJobs } from "./cleanupOldJobs";
import { matchOrCreateCompany } from "../lib/matchOrCreateCompany.js";
import {
  cleanJobTitle,
  cleanCompanyName,
  cleanLocation,
  initLocationCache,
} from "../lib/cleanJobData";
import { handleCaptchaIfPresent } from "../scripts/improvedCaptchaSolver";
import pLimit from "p-limit";
import { chromium } from "playwright";
import { searchBingForJobs } from "../lib/search/bingJobSearch";

const prisma = new PrismaClient();

// Make CONCURRENCY globally accessible so it can be modified by runFastJobScraper
declare global {
  var CONCURRENCY: number;
}

// Set default concurrency if not already set
if (global.CONCURRENCY === undefined) {
  global.CONCURRENCY = 5;
}

// Add this function near the top of the file
async function hasJobBeenProcessedRecently(
  jobUrl: string,
  daysThreshold = 7
): Promise<boolean> {
  try {
    // Check if this job URL exists in the database and was processed recently
    const existingJob = await prisma.jobListing.findFirst({
      where: {
        url: jobUrl,
        createdAt: {
          gte: new Date(Date.now() - daysThreshold * 24 * 60 * 60 * 1000), // Within the last X days
        },
      },
    });

    return !!existingJob;
  } catch (error) {
    logger.error(`❌ Error checking if job was processed recently:`, error);
    return false; // Assume not processed if there's an error
  }
}

async function hasOccupationBeenProcessedRecently(
  occupationId: string,
  cityName: string,
  stateName: string,
  hoursThreshold = 24
): Promise<boolean> {
  try {
    // Check if this occupation/city combination has been processed recently
    const recentProgress = await prisma.scrapeProgress.findFirst({
      where: {
        metadata: {
          contains: `"occupationTitle":"${occupationId}","cityName":"${cityName}","stateName":"${stateName}"`,
        },
        updatedAt: {
          gte: new Date(Date.now() - hoursThreshold * 60 * 60 * 1000), // Within the last X hours
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    return !!recentProgress;
  } catch (error) {
    logger.error(
      `❌ Error checking if occupation was processed recently:`,
      error
    );
    return false; // Assume not processed if there's an error
  }
}

// We're using Google Jobs directly now, no need to specify job sites

export async function fastJobScraper(
  options: {
    startFromParallel: any;
    useProxy?: boolean;
    startFromOccupationId?: string;
    startFromOccupationTitle?: string;
  } = {
    startFromParallel: undefined,
  }
) {
  const useProxy = options.useProxy !== false; // Default to true if not specified
  const startFromOccupationId = options.startFromOccupationId; // Start from a specific occupation ID if provided
  const limit = pLimit(global.CONCURRENCY);

  logger.info(`🔄 Running with concurrency: ${global.CONCURRENCY}`);

  // Initialize the location cache for city name matching
  logger.info("🌍 Initializing location cache for city name matching...");
  await initLocationCache();

  // Get US states only
  const stateId = await prisma.state.findFirst({
    where: { name: "California" },
  });

  // FOCUS: Just use one state for debugging
  if (!stateId) {
    logger.error("❌ Could not find the specified state in the state table");
    return;
  }

  const cities = await prisma.city.findMany({
    where: {
      stateId: stateId.id,
    },
    select: {
      name: true,
      state: {
        select: {
          id: true,
          code: true,
          name: true,
        },
      },
    },
    take: 1,
  });

  logger.info(`🌎 Processing state: ${stateId.name} - City: ${cities[0].name}`);

  // Get occupations from the database
  const occupations = await prisma.occupations.findMany({
    select: {
      id: true,
      title: true,
    },
    orderBy: {
      title: "asc",
    },
  });

  // Check if we have a progress record to resume from
  let lastProgress = null;

  try {
    // First check if we should continue from parallelJobScraper's progress
    if (options.startFromParallel) {
      const parallelProgress = await prisma.scrapeProgress.findFirst({
        where: { type: "parallelJobScraper" },
        orderBy: { updatedAt: "desc" },
      });

      if (parallelProgress) {
        logger.info(`🔄 Continuing from parallelJobScraper progress`);

        // Parse the metadata to get additional information if available
        let metadataObj: {
          lastBatchId?: string;
          lastOccupationTitle?: string;
          lastCityName?: string;
          lastStateCode?: string;
        } = {};
        try {
          metadataObj = JSON.parse(parallelProgress.metadata || "{}");
          logger.info(
            `📊 Metadata from parallelJobScraper: ${JSON.stringify(metadataObj)}`
          );
        } catch (e) {
          logger.warn(
            `⚠️ Could not parse metadata from parallelJobScraper: ${e}`
          );
        }

        lastProgress = {
          lastOccupationId: parallelProgress.lastOccupationId,
          lastCityIndex: parallelProgress.lastCityIndex,
          sourceType: "parallelJobScraper",
          lastBatchId: metadataObj.lastBatchId || null,
          lastOccupationTitle: metadataObj.lastOccupationTitle || null,
          lastCityName: metadataObj.lastCityName,
          lastStateCode: metadataObj.lastStateCode,
        };

        logger.info(
          `💾 Found parallelJobScraper progress: Last occupation ID: ${lastProgress.lastOccupationId || "None"}, Last city index: ${lastProgress.lastCityIndex || 0}`
        );

        if (lastProgress.lastBatchId) {
          logger.info(`💾 Last batch ID: ${lastProgress.lastBatchId}`);
        }

        if (lastProgress.lastOccupationTitle) {
          logger.info(
            `💾 Last occupation title: ${lastProgress.lastOccupationTitle}`
          );
        }
      }
    }

    // If not continuing from parallel or no parallel progress found, check for fastJobScraper progress
    if (!lastProgress) {
      const progressRecord = await prisma.scrapeProgress.findFirst({
        where: { type: "fastJobScraper" },
        orderBy: { updatedAt: "desc" },
      });

      if (progressRecord) {
        // If we have metadata as a string, parse it
        if (progressRecord.metadata) {
          try {
            const metadata = JSON.parse(progressRecord.metadata);
            lastProgress = {
              lastOccupationId: progressRecord.lastOccupationId,
              lastCityIndex: progressRecord.lastCityIndex,
              metadata,
              sourceType: "fastJobScraper",
            };
          } catch (parseError) {
            // If parsing fails, use the raw data
            lastProgress = {
              lastOccupationId: progressRecord.lastOccupationId,
              lastCityIndex: progressRecord.lastCityIndex,
              sourceType: "fastJobScraper",
            };
          }
        } else {
          // If no metadata, just use the basic fields
          lastProgress = {
            lastOccupationId: progressRecord.lastOccupationId,
            lastCityIndex: progressRecord.lastCityIndex,
            sourceType: "fastJobScraper",
          };
        }

        logger.info(
          `💾 Found fastJobScraper progress record: ${JSON.stringify(lastProgress)}`
        );
      } else {
        logger.info(`💾 No progress record found, starting from the beginning`);
      }
    }
  } catch (error) {
    logger.error(`❌ Error reading progress from database:`, error);
  }

  // If we have a progress record or startFromOccupationId, determine where to start from
  let startOccupationIndex = 0;
  let startCityIndex = 0;

  // First check if we have a startFromOccupationTitle parameter
  if (options.startFromOccupationTitle) {
    const titleIndex = occupations.findIndex(
      (o) => o.title === options.startFromOccupationTitle
    );
    if (titleIndex !== -1) {
      // Start from the NEXT occupation after the specified one
      startOccupationIndex = titleIndex + 1;
      if (startOccupationIndex < occupations.length) {
        logger.info(
          `🔄 Starting from next occupation after: ${options.startFromOccupationTitle} (${occupations[startOccupationIndex].title})`
        );
      } else {
        logger.info(`🔄 Reached the end of occupations list, starting over`);
        startOccupationIndex = 0;
      }
    } else {
      logger.warn(
        `⚠️ Could not find occupation with title: ${options.startFromOccupationTitle}, starting from the beginning`
      );
    }
  }
  // If no startFromOccupationTitle or it wasn't found, check for lastProgress
  else if (lastProgress) {
    // First try to use the occupationIndex from metadata if available
    if (
      lastProgress.metadata &&
      lastProgress.metadata.occupationIndex !== undefined
    ) {
      // Start from the NEXT occupation after the one in the metadata
      startOccupationIndex = lastProgress.metadata.occupationIndex + 1;

      // Verify the index is valid
      if (
        startOccupationIndex >= 0 &&
        startOccupationIndex < occupations.length
      ) {
        logger.info(
          `🔄 Moving to next occupation: ${occupations[startOccupationIndex].title} (index: ${startOccupationIndex})`
        );
      } else {
        logger.warn(
          `⚠️ Invalid next occupation index: ${startOccupationIndex}, resetting to beginning`
        );
        startOccupationIndex = 0;
      }
    }
    // If we couldn't use the index from metadata, try title lookup
    else if (lastProgress.metadata && lastProgress.metadata.occupationTitle) {
      const titleIndex = occupations.findIndex(
        (o) => o.title === lastProgress.metadata.occupationTitle
      );
      if (titleIndex !== -1) {
        // Move to the NEXT occupation
        startOccupationIndex = titleIndex + 1;
        if (startOccupationIndex < occupations.length) {
          logger.info(
            `🔄 Moving to next occupation after ${lastProgress.metadata.occupationTitle}: ${occupations[startOccupationIndex].title}`
          );
        } else {
          logger.info(`🔄 Reached the end of occupations list, starting over`);
          startOccupationIndex = 0;
        }
      } else {
        logger.warn(
          `⚠️ Could not find occupation with title: ${lastProgress.metadata.occupationTitle}, starting from the beginning`
        );
      }
    }
    // Only as a last resort, try the lastOccupationId
    else if (lastProgress.lastOccupationId) {
      const lastOccupationIndex = occupations.findIndex(
        (o) => o.id === lastProgress.lastOccupationId
      );
      if (lastOccupationIndex !== -1) {
        // Move to the NEXT occupation
        startOccupationIndex = lastOccupationIndex + 1;
        if (startOccupationIndex < occupations.length) {
          logger.info(
            `🔄 Moving to next occupation by ID: ${occupations[startOccupationIndex].title}`
          );
        } else {
          logger.info(`🔄 Reached the end of occupations list, starting over`);
          startOccupationIndex = 0;
        }
      }
    }

    // If we have a city index, use it
    if (
      lastProgress.lastCityIndex !== undefined &&
      lastProgress.lastCityIndex !== null
    ) {
      // If we're starting from the same occupation as last time, move to the next city
      if (
        startOccupationIndex ===
        occupations.findIndex((o) => o.id === lastProgress.lastOccupationId)
      ) {
        startCityIndex = lastProgress.lastCityIndex + 1; // Start from the next city

        // If we've reached the end of cities for this occupation, reset to 0
        // This shouldn't happen normally as we should move to next occupation
        if (startCityIndex >= cities.length) {
          startCityIndex = 0;
          // Move to next occupation since we've completed all cities
          startOccupationIndex++;
          logger.info(
            `🔄 Completed all cities for last occupation, moving to next occupation`
          );
        } else {
          logger.info(
            `🔄 Starting from NEXT city after last processed: index ${startCityIndex}`
          );
        }
      } else {
        // If we're starting from a different occupation, start from the first city
        startCityIndex = 0;
        logger.info(`🔄 Starting from first city for new occupation`);
      }
    }
  }

  // Slice the occupations array to start from where we left off
  const occupationsToProcess = occupations.slice(startOccupationIndex);

  logger.info(`💼 Processing ${occupations.length} occupations in total`);

  // We no longer use CAPTCHA-solving extensions
  // Instead, we detect CAPTCHAs and restart the browser

  // Launch browser with additional arguments to avoid detection
  // No need for persistent context or user data directory anymore
  logger.info(
    `🌐 Launching browser with proxy: ${useProxy ? "enabled" : "disabled"}`
  );

  // Use production environment to determine headless mode
  const isProduction = process.env.NODE_ENV === "production";
  const slowMo = isProduction ? 20 : 50; // Less slowdown in headless mode

  const browser = await chromium.launch({
    headless: isProduction, // Use headless in production
    slowMo,
    args: [
      "--disable-blink-features=AutomationControlled",
      "--no-sandbox",
      "--disable-web-security",
      "--disable-features=IsolateOrigins,site-per-process",
    ],
  });

  logger.info(
    `🖥️ Browser launched in ${isProduction ? "headless" : "non-headless"} mode with slowMo=${slowMo}`
  );

  // Create a context with the desired viewport size
  let contextOptions: any = {
    viewport: { width: 1280, height: 800 },
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  };

  // Add proxy configuration if enabled
  if (useProxy) {
    // Get proxy configuration from environment variables
    const SMARTPROXY_USERNAME = process.env.SMARTPROXY_USERNAME || "sp1234";
    const SMARTPROXY_PASSWORD = process.env.SMARTPROXY_PASSWORD || "password";
    const SMARTPROXY_HOST = process.env.SMARTPROXY_HOST || "us.smartproxy.com";
    const SMARTPROXY_PORT = process.env.SMARTPROXY_PORT || "10000";

    contextOptions.proxy = {
      server: `http://${SMARTPROXY_HOST}:${SMARTPROXY_PORT}`,
      username: SMARTPROXY_USERNAME,
      password: SMARTPROXY_PASSWORD,
    };

    logger.info(`🔒 Using proxy: ${SMARTPROXY_HOST}:${SMARTPROXY_PORT}`);
  } else {
    logger.info(`🔓 Running without proxy`);
  }

  let context = await browser.newContext(contextOptions);

  // Create a page and set extra HTTP headers to appear more human-like
  let page = await context.newPage();
  await page.setExtraHTTPHeaders({
    "Accept-Language": "en-US,en;q=0.9",
    Accept:
      "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
  });

  // Process occupations starting from where we left off
  for (
    let occupationIndex = 0;
    occupationIndex < occupationsToProcess.length;
    occupationIndex++
  ) {
    const occupation = occupationsToProcess[occupationIndex];
    // Calculate the actual index in the full occupations array for progress tracking
    const actualOccupationIndex = occupationIndex + startOccupationIndex;
    logger.info(
      `💼 Processing occupation: ${occupation.title} (${
        actualOccupationIndex + 1
      }/${occupations.length})`
    );

    // Get all cities if we're starting a new occupation, or slice from where we left off
    const citiesToProcess =
      occupationIndex === startOccupationIndex
        ? cities.slice(startCityIndex)
        : cities;

    logger.info(
      `🌎 Processing ${citiesToProcess.length} cities for ${occupation.title}`
    );

    // Process all cities for this occupation
    for (let cityIndex = 0; cityIndex < citiesToProcess.length; cityIndex++) {
      const city = citiesToProcess[cityIndex];
      logger.info(
        `🌍 [${city.name}, ${city.state.code}] Processing for ${
          occupation.title
        } (${cityIndex + 1}/${citiesToProcess.length})`
      );

      const wasProcessedRecently = await hasOccupationBeenProcessedRecently(
        occupation.id,
        city.name,
        city.state.name
      );

      if (wasProcessedRecently) {
        logger.info(
          `⏭️ Skipping recently processed occupation/city: ${occupation.title} in ${city.name}, ${city.state.name}`
        );
        continue; // Skip to the next occupation/city combination
      }

      await limit(async () => {
        try {
          // Go directly to the Google Jobs search URL
          logger.info(`🌐 Going directly to Google Jobs search URL...`);

          // 1) Now proceed with the actual search
          const searchQuery = encodeURIComponent(
            `${occupation.title} jobs ${city.name}, ${city.state.code}`
          );
          const jobsUrl = `https://www.bing.com/jobs?q=${searchQuery}&setlang=en-US&cc=US&ensearch=1`;

          // Note: searchGoogleForJobs will also log this URL, so we don't need to log it here

          await page.goto(jobsUrl, {
            waitUntil: "networkidle",
            timeout: 60000, // 60 second timeout
          });

          // Wait to ensure page is fully loaded
          await delay(5000);

          // 2) Check if we got a CAPTCHA challenge
          const { captchaDetected } = await handleCaptchaIfPresent(page);

          // If CAPTCHA was detected, restart Chrome
          if (captchaDetected) {
            logger.warn(
              "🕐 CAPTCHA detected! Restarting Chrome to avoid being blocked..."
            );

            // No need to take screenshots of CAPTCHAs anymore

            // Close the current page
            logger.info("🔒 Closing current page...");
            await page.close();

            logger.info("🔓 Creating new page...");

            // Create a new context with the same options
            context = await browser.newContext(contextOptions);

            // Create a new page
            page = await context.newPage();

            // Set extra HTTP headers to appear more human-like
            await page.setExtraHTTPHeaders({
              "Accept-Language": "en-US,en;q=0.9",
              Accept:
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            });

            // Wait longer before continuing to ensure IP cooldown
            logger.info("⏳ Waiting 30 seconds before continuing...");
            await delay(30000);

            // Go to Google first to check if CAPTCHA shows before making any search queries
            logger.info("🌐 Navigating to Google to check for CAPTCHA...");
            await page.goto("https://www.google.com", {
              waitUntil: "networkidle",
              timeout: 60000, // 60 second timeout
            });

            // Wait longer to ensure page is fully loaded
            logger.info("⏳ Waiting for page to settle...");
            await delay(10000);

            // Check if we still have a CAPTCHA
            const { captchaDetected: stillHasCaptcha } =
              await handleCaptchaIfPresent(page);

            if (stillHasCaptcha) {
              logger.error(
                "❌ Still getting CAPTCHA after browser restart. Skipping this search."
              );
              return; // Skip this city-occupation pair
            }

            // Try the search again with longer timeouts
            logger.info(
              `🔄 Retrying search for ${occupation.title} in ${city.name}, ${city.state.code}...`
            );

            // Note: We're reusing the same jobsUrl from above
            await page.goto(jobsUrl, {
              waitUntil: "networkidle",
              timeout: 60000, // 60 second timeout
            });

            // Wait longer to ensure page is fully loaded
            logger.info("⏳ Waiting for search results to load completely...");
            await delay(15000);

            // Check one more time for CAPTCHA
            const { captchaDetected: captchaAfterRetry } =
              await handleCaptchaIfPresent(page);

            if (captchaAfterRetry) {
              logger.error(
                "❌ Still getting CAPTCHA after retry. Skipping this search."
              );
              return; // Skip this city-occupation pair
            }

            logger.info(
              "✅ Successfully restarted browser and avoided CAPTCHA!"
            );
          }

          // 3) Now we parse the job listings with searchGoogleForJobs
          //    which expects (page, jobTitle, city)
          //    The page is already at the correct URL
          const jobs = await searchBingForJobs(
            page,
            occupation.title,
            `${city.name}, ${city.state.code}`
          );

          logger.info(`🧪 Scraped ${jobs.length} raw results`);

          if (jobs.length > 0) {
            // Log some sample results
            const samplesToLog = Math.min(jobs.length, 3); // Log at most 3 samples
            for (let i = 0; i < samplesToLog; i++) {
              const job = jobs[i];
              logger.info(`🧾 ${job.title} @ ${job.company} – ${job.url}`);
            }

            // If there are more results than we logged, indicate that
            if (jobs.length > samplesToLog) {
              logger.info(
                `... and ${jobs.length - samplesToLog} more jobs found`
              );
            }

            // Process and save the results
            logger.info(
              `💾 Processing and saving ${jobs.length} jobs to database...`
            );

            // Clean job data and match or create companies
            const cleanedJobs = jobs.map((job) => ({
              platform: "bing", // Changed from "google" to "bing"
              ...job,
              title: cleanJobTitle(job.title),
              company: cleanCompanyName(job.company),
              location: cleanLocation(job.location),
            }));

            // Log cleaned job data examples
            if (cleanedJobs.length > 0) {
              const sampleJob = cleanedJobs[0];
              logger.info(`🧹 Cleaned job data example:`);
              logger.info(
                `  Original title: "${jobs[0].title}" → Cleaned: "${sampleJob.title}"`
              );
              logger.info(
                `  Original company: "${jobs[0].company}" → Cleaned: "${sampleJob.company}"`
              );
              logger.info(
                `  Original location: "${jobs[0].location}" → Cleaned: "${sampleJob.location}"`
              );
            }

            // Match or create companies for all jobs and store the company IDs
            const companyIds = new Map<string, string>();
            for (const job of cleanedJobs) {
              // For now, just pass the company name since FastJob doesn't have applyLink or description
              // These will be added later in the enrichment process
              // Pass the state ID to associate with the company
              const companyId = await matchOrCreateCompany(
                job.company,
                undefined,
                city.state.id
              );
              if (companyId) {
                companyIds.set(job.company, companyId);
              }
            }

            // Log company matching results
            logger.info(
              `🏢 Matched/created ${companyIds.size} companies for ${cleanedJobs.length} jobs`
            );

            // After jobs are saved, update them to connect to companies
            // This is a separate step because createMany doesn't support relations

            // Save jobs to database with fingerprint deduplication disabled
            const insertedCount = await saveJobsToDatabase(
              cleanedJobs,
              25,
              false
            );
            // Or if you implemented the forceInsert parameter:
            // const insertedCount = await saveJobsToDatabase(cleanedJobs, 25, true, true);

            // Connect jobs to companies and states using the new relationships
            if (insertedCount > 0) {
              try {
                // Log the company matches
                logger.info(`💾 Connecting jobs to companies and states:`);
                let i = 0;
                let companyConnectedCount = 0;
                let stateConnectedCount = 0;

                // For each job, update it with the company ID and state ID
                for (const job of cleanedJobs) {
                  const companyId = companyIds.get(job.company);
                  const updateData: any = {};

                  // Add company ID if available
                  if (companyId) {
                    updateData.companyId = companyId;
                    companyConnectedCount++;
                  }

                  // Add state ID
                  updateData.stateId = city.state.id;
                  stateConnectedCount++;

                  // Find the job by URL (which is unique) and update it
                  await prisma.jobListing.updateMany({
                    where: { url: job.url },
                    data: updateData,
                  });

                  // Log a sample of the connections
                  if (i < 5) {
                    logger.info(
                      `  - "${job.title}" @ "${job.company}" → ` +
                        `Company ID: ${companyId || "N/A"}, State: ${
                          city.state.name
                        } (${city.state.code})`
                    );
                  }
                  i++;
                }

                if (i > 5) {
                  logger.info(`  - ... and ${i - 5} more connections`);
                }

                logger.info(
                  `🔗 Connected ${companyConnectedCount} jobs to companies and ${stateConnectedCount} jobs to states`
                );
              } catch (err) {
                logger.error(
                  `❌ Error connecting jobs to companies/states: ${err}`
                );
              }
            }

            // Check if jobs were successfully saved
            if (insertedCount > 0) {
              logger.info(
                `✅ Successfully saved ${insertedCount} jobs to database`
              );
              logger.info(
                `✅ [${city.name}, ${city.state.code}] ${occupation.title}: ${insertedCount} jobs saved`
              );

              // Log information about the saved jobs
              logger.info(`📊 Job details:`);
              logger.info(
                `  - isProcessing: false (all jobs start as not being processed)`
              );
              logger.info(
                `  - createdAt: Set automatically by Prisma for new jobs`
              );
              logger.info(`  - lastCheckedAt: ${new Date().toISOString()}`);

              // Log a sample of the cleaned jobs
              if (cleanedJobs.length > 0) {
                const sampleJob = cleanedJobs[0];
                logger.info(
                  `📝 Sample job: ${sampleJob.title} @ ${sampleJob.company}`
                );
                logger.info(`  - URL: ${sampleJob.url}`);
                logger.info(`  - Location: ${sampleJob.location}`);
              }
            } else {
              logger.warn(
                `⚠️ Found ${jobs.length} jobs but none were saved to database`
              );
              logger.info(
                `✅ [${city.name}, ${city.state.code}] ${occupation.title}: 0 jobs saved`
              );
            }
          } else {
            logger.warn(
              `⚠️ No jobs found for ${occupation.title} in ${city.name}, ${city.state.code}`
            );
            logger.info(
              `✅ [${city.name}, ${city.state.code}] ${occupation.title}: 0 jobs`
            );
          }
        } catch (err) {
          logger.error(
            `❌ [${city.name}, ${city.state.code}] ${occupation.title} failed`,
            err
          );
        }

        // small delay to avoid constant rapid queries
        await delay(1000);
      });

      // Update progress after each city is processed
      try {
        // Create metadata object that preserves information from parallelJobScraper if available
        const metadata = {
          timestamp: new Date().toISOString(),
          occupationTitle: occupation.title,
          cityName: city.name,
          stateCode: city.state.code,
          stateName: city.state.name,
          occupationIndex: actualOccupationIndex,
          // Include the last batch ID if we started from parallelJobScraper
          lastBatchId: lastProgress?.lastBatchId || null,
          // Add a note that this was processed by fastJobScraper
          processedBy: "fastJobScraper",
          // Include these fields to maintain compatibility
          completedBatches: 0,
          successfulBatches: 0,
          totalBatches: 0,
          totalJobsFound: 0,
          totalJobsSaved: 0,
        };

        // First check if a fastJobScraper record exists
        const existingRecord = await prisma.scrapeProgress.findFirst({
          where: { type: "fastJobScraper" },
          orderBy: { updatedAt: "desc" },
        });

        if (existingRecord) {
          logger.info(
            `💾 DEBUG: Found existing fastJobScraper progress record with ID: ${existingRecord.id}`
          );

          // Update the existing record
          try {
            const updatedRecord = await prisma.scrapeProgress.update({
              where: { id: existingRecord.id },
              data: {
                lastOccupationIndex: actualOccupationIndex,
                lastCityIndex: cityIndex,
                metadata: JSON.stringify(metadata),
                updatedAt: new Date(),
              },
            });

            logger.info(
              `💾 Updated existing fastJobScraper progress record with ID: ${existingRecord.id}`
            );
          } catch (updateError) {
            logger.error(`❌ Error updating progress record: ${updateError}`);
            // Create new record as fallback...
          }
        } else {
          // Create new record...
        }

        // ADDED: Also update the parallelJobScraper record if we started from it
        if (options.startFromParallel) {
          const parallelRecord = await prisma.scrapeProgress.findFirst({
            where: { type: "parallelJobScraper" },
            orderBy: { updatedAt: "desc" },
          });

          if (parallelRecord) {
            logger.info(
              `💾 DEBUG: Found existing parallelJobScraper progress record with ID: ${parallelRecord.id}`
            );

            // Create parallel metadata that includes fastJobScraper's progress
            const parallelMetadata = {
              ...JSON.parse(parallelRecord.metadata || "{}"),
              lastUpdatedBy: "fastJobScraper",
              lastUpdatedAt: new Date().toISOString(),
              lastOccupationTitle: occupation.title,
              lastCityName: city.name,
              lastStateCode: city.state.code,
              lastStateName: city.state.name,
            };

            try {
              const updatedParallelRecord = await prisma.scrapeProgress.update({
                where: { id: parallelRecord.id },
                data: {
                  lastOccupationIndex: actualOccupationIndex,
                  lastCityIndex: cityIndex,
                  metadata: JSON.stringify(parallelMetadata),
                  updatedAt: new Date(),
                },
              });

              logger.info(
                `💾 Updated existing parallelJobScraper progress record with ID: ${parallelRecord.id}`
              );
            } catch (updateError) {
              logger.error(
                `❌ Error updating parallelJobScraper progress record: ${updateError}`
              );
            }
          } else {
            logger.warn(
              `⚠️ No parallelJobScraper progress record found to update`
            );
          }
        }
      } catch (progressError) {
        logger.error(`❌ Failed to save progress to database:`, progressError);
      }
    }
  }

  logger.info("✅ Fast job scrape complete.");
  await cleanupOldJobs();
  await context.close();
  await browser.close();
}

function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Export a function to run the fastJobScraper with options
export async function runFastJobScraper(
  options: {
    useProxy?: boolean;
    startFromOccupationId?: string;
    startFromOccupationTitle?: string;
    maxConcurrency?: number; // Added support for controlling concurrency
  } = {}
) {
  try {
    // If maxConcurrency is provided, temporarily override the global CONCURRENCY value
    let originalConcurrency;
    if (options.maxConcurrency !== undefined) {
      originalConcurrency = global.CONCURRENCY;
      global.CONCURRENCY = options.maxConcurrency;
      logger.info(
        `🔄 Temporarily setting concurrency to ${options.maxConcurrency}`
      );
    }

    logger.info(
      `🚀 Starting fastJobScraper with options: ${JSON.stringify(options)}`
    );

    await fastJobScraper({
      startFromParallel: undefined,
      ...options,
    });

    // Restore original concurrency if it was changed
    if (originalConcurrency !== undefined) {
      global.CONCURRENCY = originalConcurrency;
      logger.info(
        `🔄 Restored concurrency to original value: ${originalConcurrency}`
      );
    }

    logger.info(`✅ fastJobScraper completed successfully`);
    return true;
  } catch (error) {
    logger.error(`❌ Error running fastJobScraper:`, error);
    return false;
  }
}

// Run the scraper if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  // Parse command line arguments
  const args = process.argv.slice(2);
  const useProxy = !args.includes("--no-proxy");

  runFastJobScraper({ useProxy });
}

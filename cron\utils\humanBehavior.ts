// cron/utils/humanBehavior.ts
import { Page } from "playwright";
import { logger } from "./logger";

// Delay function with randomization to appear more human-like
export const delay = (ms: number, randomFactor = 0.2) => {
  // Add random variation to the delay (±randomFactor%)
  const randomizedDelay =
    ms * (1 + (Math.random() * 2 * randomFactor - randomFactor));
  return new Promise((resolve) => setTimeout(resolve, randomizedDelay));
};

// Advanced human-like random delay between actions with variable timing patterns
export const humanDelay = async (action?: string) => {
  // Check if we're in production mode - use faster delays for production
  const isProduction = process.env.NODE_ENV === "production";

  // Different actions have different typical human timing patterns
  let baseDelay: number;
  let variability: number;

  if (isProduction) {
    // Much faster delays for production mode
    switch (action) {
      case "navigation":
        baseDelay = 500;
        variability = 500;
        break;
      case "scroll":
        baseDelay = 100;
        variability = 200;
        break;
      case "click":
        baseDelay = 50;
        variability = 150;
        break;
      case "typing":
        baseDelay = 50;
        variability = 100;
        break;
      case "thinking":
        baseDelay = 300;
        variability = 500;
        break;
      case "reading":
        baseDelay = 500;
        variability = 1000;
        break;
      default:
        baseDelay = 200;
        variability = 300;
    }
  } else {
    // Original slower delays for development mode (to avoid detection)
    switch (action) {
      case "navigation":
        baseDelay = 2000;
        variability = 2000;
        break;
      case "scroll":
        baseDelay = 500;
        variability = 1500;
        break;
      case "click":
        baseDelay = 300;
        variability = 1200;
        break;
      case "typing":
        baseDelay = 200;
        variability = 600;
        break;
      case "thinking":
        baseDelay = 3000;
        variability = 5000;
        break;
      case "reading":
        baseDelay = 5000;
        variability = 10000;
        break;
      default:
        baseDelay = 1000;
        variability = 4000;
    }
  }

  // Add some randomness to the delay
  const randomMs = baseDelay + Math.floor(Math.random() * variability);

  // Occasionally add an extra long pause (only 1% chance in production, 5% in development)
  if (Math.random() < (isProduction ? 0.01 : 0.05)) {
    const extraDelay = isProduction
      ? 500 + Math.floor(Math.random() * 1000)
      : 2000 + Math.floor(Math.random() * 3000);

    logger.info(
      `🧠 Adding extra "thinking" time of ${Math.round(extraDelay / 1000)}s...`
    );
    await delay(extraDelay);
  }

  await delay(randomMs);
};

// Simulate human-like scrolling behavior
export const humanScroll = async (page: Page, scrollAmount: number = 300) => {
  // Determine number of scroll steps (1-5)
  const scrollSteps = 1 + Math.floor(Math.random() * 4);
  const scrollStep = scrollAmount / scrollSteps;

  for (let i = 0; i < scrollSteps; i++) {
    // Random scroll amount per step
    const thisStep = scrollStep + (Math.random() * 50 - 25);

    await page.evaluate((scrollY: number) => {
      window.scrollBy(0, scrollY);
    }, thisStep);

    // Random delay between scroll steps (100-500ms)
    await delay(100 + Math.floor(Math.random() * 400));
  }

  // Pause after scrolling
  await humanDelay("scroll");
};

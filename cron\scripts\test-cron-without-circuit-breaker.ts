#!/usr/bin/env node

/**
 * Test cron job execution without circuit breaker interference
 *
 * This script bypasses the circuit breaker to test if that's what's
 * preventing cron jobs from running.
 */

const { logger } = require("../utils/logger.js");
const { spawn } = require("child_process");

async function testCronWithoutCircuitBreaker() {
  logger.info("🧪 Testing cron job execution without circuit breaker");

  try {
    // Test a simple command first
    logger.info("📋 Testing simple command execution...");

    const testCommand = async (
      command: string,
      args: string[],
      description: string
    ) => {
      logger.info(`🔄 Running ${description}: ${command} ${args.join(" ")}`);

      return new Promise<void>((resolve, reject) => {
        const child = spawn(command, args, {
          cwd: process.cwd(),
          stdio: ["ignore", "pipe", "pipe"],
          shell: true,
        });

        let stdout = "";
        let stderr = "";

        child.stdout.on("data", (chunk) => {
          const output = chunk.toString();
          stdout += output;
          logger.info(`📤 ${description} stdout: ${output.trim()}`);
        });

        child.stderr.on("data", (chunk) => {
          const output = chunk.toString();
          stderr += output;
          logger.warn(`⚠️ ${description} stderr: ${output.trim()}`);
        });

        child.on("error", (err) => {
          logger.error(`❌ ${description} failed to start:`, err);
          reject(err);
        });

        child.on("close", (code, signal) => {
          if (signal) {
            logger.error(`❌ ${description} killed by signal ${signal}`);
            reject(new Error(`Process killed by signal ${signal}`));
          } else if (code !== 0) {
            logger.error(`❌ ${description} exited with code ${code}`);
            reject(new Error(`Process exited with code ${code}`));
          } else {
            logger.info(`✅ ${description} completed successfully`);
            resolve();
          }
        });

        // Add a timeout for safety
        setTimeout(() => {
          child.kill();
          reject(new Error(`${description} timed out after 30 seconds`));
        }, 30000);
      });
    };

    // Test 1: Simple echo command
    await testCommand("echo", ["Hello from cron test"], "Simple echo test");

    // Test 2: Node version check
    await testCommand("node", ["--version"], "Node version check");

    // Test 3: NPM command test
    await testCommand("npm", ["--version"], "NPM version check");

    // Test 4: Check if we can run a TypeScript file
    try {
      await testCommand("npx", ["tsx", "--version"], "TSX version check");
    } catch (error) {
      logger.warn("⚠️ TSX not available, trying alternative...");
    }

    // Test 5: Try to run a simple cron job script (if it exists)
    logger.info("🔍 Looking for available cron job scripts...");

    // Check what scripts are available in package.json
    try {
      await testCommand("npm", ["run"], "Available NPM scripts");
    } catch (error) {
      logger.warn("⚠️ Could not list NPM scripts");
    }

    logger.info("✅ Basic command execution tests completed");

    // Now test the actual issue - try to run a cron job manually
    logger.info("🎯 Testing manual cron job execution...");

    // Try to run the parallel job scraper in test mode
    try {
      logger.info("🔄 Attempting to run parallel job scraper in test mode...");
      await testCommand(
        "npm",
        ["run", "parallel-test"],
        "Parallel job scraper test"
      );
    } catch (error) {
      logger.warn(
        "⚠️ Parallel job scraper test failed or not available:",
        error
      );
    }

    // Try other common cron jobs
    const commonJobs = [
      ["daily-summary", "Daily summary job"],
      ["market-analytics", "Market analytics job"],
      ["check-exp", "Experience requirements check"],
    ];

    for (const [script, description] of commonJobs) {
      try {
        logger.info(`🔄 Testing ${description}...`);
        await testCommand("npm", ["run", script], description);
      } catch (error) {
        logger.warn(`⚠️ ${description} failed or not available:`, error);
      }
    }

    logger.info("🎉 Cron job testing completed!");
    logger.info("📋 Summary:");
    logger.info("  • Basic commands: Working");
    logger.info("  • Node/NPM: Working");
    logger.info("  • Cron job scripts: Check individual results above");

    logger.info("🔧 If basic commands work but cron jobs don't:");
    logger.info(
      "  1. The issue is likely in the circuit breaker or job scheduling logic"
    );
    logger.info("  2. Check if jobs are marked as 'running' and stuck");
    logger.info("  3. Verify the circuit breaker state");
    logger.info("  4. Check Redis connectivity and data");
  } catch (error) {
    logger.error("❌ Cron test failed:", error);
  }
}

async function main() {
  try {
    await testCronWithoutCircuitBreaker();
    process.exit(0);
  } catch (error) {
    logger.error("❌ Test script failed:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

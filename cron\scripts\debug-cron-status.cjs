#!/usr/bin/env node

/**
 * Debug script to check why cron jobs aren't running
 * 
 * This script checks:
 * 1. Circuit breaker status
 * 2. Redis connectivity
 * 3. Worker health status
 * 4. System resources
 * 5. Cron job configuration
 */

async function debugCronStatus() {
  console.log("🔍 Starting cron job debug analysis");
  
  try {
    // Import modules dynamically since we're in CommonJS
    const { logger } = await import("../utils/logger.js");
    const { getSharedCircuitBreaker } = await import("../utils/sharedCircuitBreaker.js");
    const { getContainerMetrics } = await import("../utils/containerMetrics.js");
    const { redis } = await import("../../workers/redis.js");
    
    // 1. Check Redis connectivity
    console.log("📡 Testing Redis connectivity...");
    try {
      await redis.ping();
      console.log("✅ Redis is connected and responding");
    } catch (error) {
      console.error("❌ Redis connection failed:", error);
      return;
    }
    
    // 2. Check circuit breaker status
    console.log("🧠 Checking circuit breaker status...");
    try {
      const circuitBreaker = getSharedCircuitBreaker();
      const isClosed = await circuitBreaker.isClosed();
      const state = circuitBreaker.getState();
      
      console.log(`🔧 Circuit breaker state: ${state}`);
      console.log(`🔧 Circuit breaker closed (jobs allowed): ${isClosed}`);
      
      if (!isClosed) {
        console.warn("⚠️ Circuit breaker is OPEN - this may be preventing cron jobs from running!");
      }
    } catch (error) {
      console.error("❌ Failed to check circuit breaker:", error);
    }
    
    // 3. Check system resources
    console.log("📊 Checking system resources...");
    try {
      const containerMetrics = await getContainerMetrics();
      
      if (containerMetrics) {
        console.log(`💾 Memory usage: ${containerMetrics.memoryUsagePercent.toFixed(2)}%`);
        console.log(`🖥️ CPU usage: ${containerMetrics.cpuUsagePercent.toFixed(2)}%`);
        
        if (containerMetrics.memoryUsagePercent > 80) {
          console.warn("⚠️ High memory usage detected!");
        }
        
        if (containerMetrics.cpuUsagePercent > 80) {
          console.warn("⚠️ High CPU usage detected!");
        }
      } else {
        console.warn("⚠️ Container metrics not available");
      }
    } catch (error) {
      console.error("❌ Failed to get system metrics:", error);
    }
    
    // 4. Check worker health data in Redis
    console.log("🏥 Checking worker health data in Redis...");
    try {
      const healthData = await redis.hgetall("worker:health");
      const metricsData = await redis.hgetall("worker:metrics");
      const circuitData = await redis.hgetall("worker:circuit");
      
      console.log(`📋 Health data entries: ${Object.keys(healthData).length}`);
      console.log(`📋 Metrics data entries: ${Object.keys(metricsData).length}`);
      console.log(`📋 Circuit data entries: ${Object.keys(circuitData).length}`);
      
      // Show health status for each worker type
      for (const [workerType, healthJson] of Object.entries(healthData)) {
        try {
          const health = JSON.parse(healthJson);
          console.log(`🔧 ${workerType}: ${health.healthy ? 'HEALTHY' : 'UNHEALTHY'} (${health.status})`);
        } catch (error) {
          console.warn(`⚠️ Failed to parse health data for ${workerType}: ${error.message}`);
        }
      }
      
      // Show circuit status for each worker type
      for (const [workerType, circuitJson] of Object.entries(circuitData)) {
        try {
          const circuit = JSON.parse(circuitJson);
          console.log(`🧠 ${workerType} circuit: ${circuit.state} (failures: ${circuit.failureCount})`);
        } catch (error) {
          console.warn(`⚠️ Failed to parse circuit data for ${workerType}: ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error("❌ Failed to check Redis health data:", error);
    }
    
    // 5. Check if any jobs are currently marked as running
    console.log("🏃 Checking for running jobs...");
    try {
      const runningJobsData = await redis.hgetall("cron:running_jobs");
      console.log(`📋 Running jobs data: ${JSON.stringify(runningJobsData, null, 2)}`);
      
      const runningCount = Object.values(runningJobsData).filter(status => status === 'true').length;
      if (runningCount > 0) {
        console.warn(`⚠️ ${runningCount} jobs are marked as currently running - this may prevent new jobs from starting`);
      } else {
        console.log("✅ No jobs are currently marked as running");
      }
    } catch (error) {
      console.warn("⚠️ Could not check running jobs status:", error);
    }
    
    // 6. Summary and recommendations
    console.log("📋 Debug Summary:");
    console.log("  • Redis connectivity: Check logs above");
    console.log("  • Circuit breaker status: Check logs above");
    console.log("  • System resources: Check logs above");
    console.log("  • Worker health: Check logs above");
    
    console.log("🔧 Next steps:");
    console.log("  1. If circuit breaker is OPEN, check why and consider resetting it");
    console.log("  2. If jobs are stuck as 'running', clear the running jobs status");
    console.log("  3. Check system resources and reduce load if necessary");
    console.log("  4. Verify cron job schedules are correct");
    
  } catch (error) {
    console.error("❌ Debug analysis failed:", error);
  }
}

async function main() {
  try {
    await debugCronStatus();
    process.exit(0);
  } catch (error) {
    console.error("❌ Debug script failed:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

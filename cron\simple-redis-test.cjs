#!/usr/bin/env node

/**
 * Simple Redis test to check and reset circuit breaker
 */

const Redis = require('ioredis');

// Load environment variables from web/.env
require('dotenv').config({ path: '../web/.env' });

console.log('🔍 Environment check:');
console.log(`DATABASE_URL: ${process.env.DATABASE_URL ? 'set' : 'not set'}`);
console.log(`REDIS_HOST: ${process.env.REDIS_HOST || 'localhost'}`);
console.log(`REDIS_PORT: ${process.env.REDIS_PORT || '6379'}`);

async function testRedis() {
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    retryStrategy: (times) => {
      console.log(`Redis retry attempt ${times}`);
      return Math.min(times * 100, 3000);
    }
  });

  try {
    console.log('📡 Testing Redis connection...');
    
    // Test Redis connection
    const pong = await redis.ping();
    console.log(`✅ Redis ping: ${pong}`);
    
    // Check current Redis data
    console.log('🔍 Checking current Redis data...');
    
    const runningJobs = await redis.hgetall('cron:running_jobs');
    const healthEntries = await redis.hgetall('worker:health');
    const circuitEntries = await redis.hgetall('worker:circuit');
    
    console.log(`📋 Running jobs: ${Object.keys(runningJobs).length}`);
    console.log(`🏥 Health entries: ${Object.keys(healthEntries).length}`);
    console.log(`🧠 Circuit entries: ${Object.keys(circuitEntries).length}`);
    
    // Show details
    if (Object.keys(runningJobs).length > 0) {
      console.log('🏃 Running jobs details:');
      for (const [job, status] of Object.entries(runningJobs)) {
        console.log(`  • ${job}: ${status}`);
      }
    }
    
    if (Object.keys(circuitEntries).length > 0) {
      console.log('🧠 Circuit breaker details:');
      for (const [worker, circuitJson] of Object.entries(circuitEntries)) {
        try {
          const circuit = JSON.parse(circuitJson);
          console.log(`  • ${worker}: ${circuit.state} (failures: ${circuit.failureCount || 0})`);
        } catch (e) {
          console.log(`  • ${worker}: Invalid data`);
        }
      }
    }
    
    // Reset everything
    console.log('🔄 Resetting circuit breaker and job states...');
    
    await redis.del('worker:circuit');
    await redis.del('cron:running_jobs');
    await redis.del('worker:health');
    
    // Set healthy worker states
    const healthData = {
      status: 'healthy',
      healthy: true,
      lastHeartbeat: new Date().toISOString(),
      hostname: require('os').hostname(),
      pid: process.pid,
    };
    
    const workerTypes = [
      'parallelJobScraper',
      'enrichJobDetails', 
      'scrapeJobDetails',
      'dailySummary',
      'marketAnalytics'
    ];
    
    for (const workerType of workerTypes) {
      await redis.hset('worker:health', workerType, JSON.stringify(healthData));
    }
    
    console.log('✅ Reset completed successfully!');
    
    // Verify reset
    const newRunningJobs = await redis.hgetall('cron:running_jobs');
    const newHealthEntries = await redis.hgetall('worker:health');
    const newCircuitEntries = await redis.hgetall('worker:circuit');
    
    console.log('📊 After reset:');
    console.log(`📋 Running jobs: ${Object.keys(newRunningJobs).length}`);
    console.log(`🏥 Health entries: ${Object.keys(newHealthEntries).length}`);
    console.log(`🧠 Circuit entries: ${Object.keys(newCircuitEntries).length}`);
    
    console.log('🎉 Circuit breaker reset complete!');
    console.log('💡 Now try running a cron job to see if it works');
    
    await redis.disconnect();
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Redis test failed:', error);
    await redis.disconnect();
    process.exit(1);
  }
}

testRedis();

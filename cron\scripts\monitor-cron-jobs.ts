// cron/scripts/monitor-cron-jobs.ts
// Script to monitor cron jobs and resource usage

import { logger } from "../utils/logger";
import { PrismaClient } from "@prisma/client";
import { getSystemResourceInfo } from "../utils/system-resources";

const prisma = new PrismaClient();

// Check the last run time of each job
async function checkJobLastRunTimes() {
  try {
    logger.info("📊 Checking last run times for jobs...");

    // Get the last run time for each job type from the database
    const jobStats = await prisma.jobStats.findMany({
      orderBy: {
        createdAt: "desc",
      },
      take: 50, // Get the last 50 job runs
    });

    // Group by job type and get the most recent for each
    const jobTypeMap = new Map<string, (typeof jobStats)[0]>();

    for (const stat of jobStats) {
      if (
        !jobTypeMap.has(stat.jobType) ||
        new Date(stat.createdAt) >
          new Date(jobTypeMap.get(stat.jobType)!.createdAt)
      ) {
        jobTypeMap.set(stat.jobType, stat);
      }
    }

    // Display the results
    logger.info("📅 Last run times for each job type:");

    if (jobTypeMap.size === 0) {
      logger.warn("⚠️ No job statistics found in the database");
    } else {
      for (const [jobType, stat] of jobTypeMap.entries()) {
        const lastRunTime = new Date(stat.createdAt);
        const now = new Date();
        const hoursSinceLastRun =
          (now.getTime() - lastRunTime.getTime()) / (1000 * 60 * 60);

        logger.info(
          `  • ${jobType}: ${lastRunTime.toLocaleString()} (${hoursSinceLastRun.toFixed(1)} hours ago)`
        );
        logger.info(
          `    - Duration: ${stat.durationMs / 1000}s, Success: ${stat.success}, Items: ${stat.itemsProcessed}`
        );
      }
    }

    return jobTypeMap;
  } catch (error) {
    logger.error("❌ Error checking job last run times:", error);
    return new Map();
  }
}

// Check for running jobs
async function checkRunningJobs() {
  try {
    logger.info("🔍 Checking for running jobs...");

    // Get the running jobs from the database
    const runningJobs = await prisma.jobStats.findMany({
      where: {
        endTime: null,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (runningJobs.length === 0) {
      logger.info("✅ No jobs are currently running");
    } else {
      logger.info(`🏃 Currently running jobs (${runningJobs.length}):`);

      for (const job of runningJobs) {
        const startTime = new Date(job.createdAt);
        const now = new Date();
        const minutesRunning =
          (now.getTime() - startTime.getTime()) / (1000 * 60);

        logger.info(
          `  • ${job.jobType}: Started at ${startTime.toLocaleString()} (${minutesRunning.toFixed(1)} minutes ago)`
        );
      }
    }

    return runningJobs;
  } catch (error) {
    logger.error("❌ Error checking running jobs:", error);
    return [];
  }
}

// Main function to monitor cron jobs
async function monitorCronJobs() {
  logger.info("🔍 Starting cron job monitoring...");

  try {
    // Check system resources
    const resourceInfo = getSystemResourceInfo();
    logger.info(`📊 System resource information:`);
    logger.info(
      `  • Memory: ${(await resourceInfo).memory.usagePercent} (${(await resourceInfo).memory.used} / ${(await resourceInfo).memory.total})`
    );
    logger.info(
      `  • CPU: ${(await resourceInfo).cpu.usagePercent} (Load: ${(await resourceInfo).cpu.loadAverage})`
    );

    // Check job last run times
    await checkJobLastRunTimes();

    // Check running jobs
    await checkRunningJobs();

    // Provide recommendations based on resource usage
    logger.info("🔧 Resource Usage Analysis:");

    if ((await resourceInfo).memory.usagePercentRaw > 90) {
      logger.warn(
        "⚠️ Memory usage is very high (>90%). This may cause the adaptive resource manager to reduce worker count significantly."
      );
      logger.info(
        "   Consider increasing available memory or optimizing jobs to use less memory."
      );
    } else if ((await resourceInfo).memory.usagePercentRaw > 80) {
      logger.info(
        "ℹ️ Memory usage is high (>80%). The adaptive resource manager may reduce worker count to manage resources."
      );
    } else {
      logger.info(
        "✅ Memory usage is at a good level for optimal job execution."
      );
    }

    if ((await resourceInfo).cpu.usagePercentRaw > 80) {
      logger.warn(
        "⚠️ CPU usage is high (>80%). This may cause the adaptive resource manager to reduce worker count."
      );
      logger.info(
        "   Consider optimizing CPU-intensive operations in your jobs."
      );
    } else {
      logger.info("✅ CPU usage is at a good level for optimal job execution.");
    }
  } catch (error) {
    logger.error("❌ Error monitoring cron jobs:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the monitoring
monitorCronJobs()
  .then(() => {
    logger.info("✅ Cron job monitoring completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Cron job monitoring failed with error:", error);
    process.exit(1);
  });

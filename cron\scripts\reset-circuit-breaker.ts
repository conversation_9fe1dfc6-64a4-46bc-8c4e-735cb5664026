// cron/scripts/reset-circuit-breaker.ts
// <PERSON><PERSON><PERSON> to reset the circuit breaker and restart the scheduler service

import { logger } from "../utils/logger";
import { CircuitBreaker, CircuitState } from "../utils/circuitBreaker";
import { startScheduledJobs, stopScheduledJobs } from "../jobs/scheduledJobs";
import os from "os";

// Function to get detailed system resource information
function getSystemResourceInfo() {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;
  
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const loadPerCpu = loadAvg / cpuCount;
  const cpuUsagePercent = loadPerCpu * 100;
  
  return {
    memory: {
      total: formatBytes(totalMemory),
      free: formatBytes(freeMemory),
      used: formatBytes(usedMemory),
      usagePercent: memoryUsagePercent.toFixed(2) + "%"
    },
    cpu: {
      count: cpuCount,
      loadAverage: loadAvg.toFixed(2),
      loadPerCpu: loadPerCpu.toFixed(2),
      usagePercent: cpuUsagePercent.toFixed(2) + "%"
    }
  };
}

// Helper function to format bytes to human-readable format
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Try to force garbage collection if available
function forceGarbageCollection() {
  if (global.gc) {
    logger.info("🧹 Running forced garbage collection...");
    
    // Get memory usage before GC
    const beforeMemory = process.memoryUsage();
    
    // Run garbage collection
    global.gc();
    
    // Get memory usage after GC
    const afterMemory = process.memoryUsage();
    
    // Calculate freed memory
    const freedHeapMemory = beforeMemory.heapUsed - afterMemory.heapUsed;
    const freedRssMemory = beforeMemory.rss - afterMemory.rss;
    
    logger.info(`✅ Garbage collection complete. Freed ${formatBytes(freedHeapMemory)} of heap memory and ${formatBytes(freedRssMemory)} of RSS memory`);
    return true;
  } else {
    logger.warn("⚠️ Garbage collection not available. Run with --expose-gc flag to enable.");
    return false;
  }
}

// Main function to reset the circuit breaker
async function resetCircuitBreaker() {
  logger.info("🔄 Starting circuit breaker reset...");
  
  try {
    // Check system resources before reset
    const beforeResources = getSystemResourceInfo();
    logger.info(`📊 System resources before reset: ${JSON.stringify(beforeResources, null, 2)}`);
    
    // Try to free up memory
    forceGarbageCollection();
    
    // Create a new circuit breaker instance
    const circuitBreaker = new CircuitBreaker({
      memoryThresholdPercent: 85,
      cpuThresholdPercent: 85,
      errorThresholdCount: 5,
      resetTimeoutMs: 10000, // 10 seconds
      checkIntervalMs: 5000, // 5 seconds
      onStateChange: (oldState, newState) => {
        logger.info(`🔄 Circuit breaker state changed from ${oldState} to ${newState}`);
      }
    });
    
    // Log initial state
    logger.info(`🔄 Initial circuit breaker state: ${circuitBreaker.getState()}`);
    
    // Force close the circuit
    logger.info("🔄 Forcing circuit breaker to closed state...");
    circuitBreaker.closeCircuit();
    
    // Log state after force close
    logger.info(`🔄 Circuit breaker state after force close: ${circuitBreaker.getState()}`);
    
    // Check system resources after reset
    const afterResources = getSystemResourceInfo();
    logger.info(`📊 System resources after reset: ${JSON.stringify(afterResources, null, 2)}`);
    
    // Stop the circuit breaker monitoring
    circuitBreaker.stopMonitoring();
    
    // Stop any running scheduled jobs
    logger.info("🛑 Stopping any running scheduled jobs...");
    stopScheduledJobs();
    
    // Wait a moment for jobs to stop
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Restart scheduled jobs
    logger.info("🚀 Restarting scheduled jobs...");
    startScheduledJobs();
    
    logger.info("✅ Circuit breaker reset and scheduled jobs restarted successfully");
    
    // Log next steps
    logger.info("📋 Next steps:");
    logger.info("1. Monitor the logs to ensure jobs are running correctly");
    logger.info("2. Check the scheduler status with 'npm run check-scheduler'");
    logger.info("3. If problems persist, consider restarting the entire scheduler service");
    
  } catch (error) {
    logger.error("❌ Error resetting circuit breaker:", error);
  }
}

// Run the reset
resetCircuitBreaker()
  .then(() => {
    logger.info("✅ Circuit breaker reset completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Circuit breaker reset failed with error:", error);
    process.exit(1);
  });

// cron/scripts/check-system-health.ts
// Script to check system health and perform recovery actions if needed

import { logger } from "../utils/logger";
import os from "os";
import { PrismaClient } from "@prisma/client";
import { sendEmailNotification, EmailNotificationType } from "../utils/emailService";
import { ImprovedImprovedCircuitBreaker, CircuitState } from "../utils/improvedImprovedCircuitBreaker";

const prisma = new PrismaClient();

// Function to format bytes to human-readable format
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Function to get detailed system resource information
function getSystemResourceInfo() {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;
  
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const loadPerCpu = loadAvg / cpuCount;
  const cpuUsagePercent = loadPerCpu * 100;
  
  return {
    memory: {
      total: formatBytes(totalMemory),
      free: formatBytes(freeMemory),
      used: formatBytes(usedMemory),
      usagePercent: memoryUsagePercent.toFixed(2) + "%"
    },
    cpu: {
      count: cpuCount,
      loadAverage: loadAvg.toFixed(2),
      loadPerCpu: loadPerCpu.toFixed(2),
      usagePercent: cpuUsagePercent.toFixed(2) + "%"
    }
  };
}

// Function to force garbage collection
function forceGarbageCollection() {
  if (global.gc) {
    logger.info("🧹 Forcing garbage collection...");
    global.gc();
    return true;
  } else {
    logger.warn("⚠️ Garbage collection not available. Run with --expose-gc flag.");
    return false;
  }
}

// Function to check for running processes that might be stuck
async function checkRunningProcesses() {
  try {
    // Check for processes that have been running for more than 2 hours
    const longRunningProcesses = await prisma.jobRun.findMany({
      where: {
        status: 'RUNNING',
        startedAt: {
          lt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
        }
      }
    });
    
    if (longRunningProcesses.length > 0) {
      logger.warn(`⚠️ Found ${longRunningProcesses.length} processes running for more than 2 hours`);
      
      // Log details of each process
      for (const process of longRunningProcesses) {
        const runningTime = Math.floor((Date.now() - process.startedAt.getTime()) / (60 * 1000));
        logger.warn(`Process ${process.id} (${process.jobType}) has been running for ${runningTime} minutes`);
      }
      
      return longRunningProcesses;
    }
    
    return [];
  } catch (error) {
    logger.error(`❌ Error checking running processes: ${error}`);
    return [];
  }
}

// Function to check for and recover from system issues
async function checkSystemHealth() {
  logger.info("🔍 Starting system health check...");
  
  // Get system resource information
  const resourceInfo = getSystemResourceInfo();
  logger.info(`📊 System resources: ${JSON.stringify(resourceInfo, null, 2)}`);
  
  // Check if memory usage is high
  const memoryUsagePercent = parseFloat(resourceInfo.memory.usagePercent);
  const cpuUsagePercent = parseFloat(resourceInfo.cpu.usagePercent);
  
  let healthIssues = [];
  
  // Check memory usage
  if (memoryUsagePercent > 80) {
    healthIssues.push(`High memory usage: ${memoryUsagePercent}%`);
    
    // Try to free up memory
    if (forceGarbageCollection()) {
      // Check memory after GC
      const postGCInfo = getSystemResourceInfo();
      const postGCMemoryUsage = parseFloat(postGCInfo.memory.usagePercent);
      const memoryImprovement = memoryUsagePercent - postGCMemoryUsage;
      
      logger.info(`🧠 Memory after GC: ${postGCMemoryUsage}% (improved by ${memoryImprovement.toFixed(2)}%)`);
      
      if (memoryImprovement < 5 && postGCMemoryUsage > 80) {
        healthIssues.push(`Memory usage remains high after GC: ${postGCMemoryUsage}%`);
      }
    }
  }
  
  // Check CPU usage
  if (cpuUsagePercent > 80) {
    healthIssues.push(`High CPU usage: ${cpuUsagePercent}%`);
  }
  
  // Check for stuck processes
  const longRunningProcesses = await checkRunningProcesses();
  if (longRunningProcesses.length > 0) {
    healthIssues.push(`${longRunningProcesses.length} processes running for more than 2 hours`);
    
    // Mark very long-running processes as failed
    for (const process of longRunningProcesses) {
      const runningTime = Math.floor((Date.now() - process.startedAt.getTime()) / (60 * 1000));
      
      // If running for more than 4 hours, mark as failed
      if (runningTime > 240) {
        try {
          await prisma.jobRun.update({
            where: { id: process.id },
            data: {
              status: 'FAILED',
              endedAt: new Date(),
              error: 'Automatically marked as failed due to excessive runtime'
            }
          });
          
          logger.info(`✅ Marked process ${process.id} as failed after ${runningTime} minutes`);
          healthIssues.push(`Marked process ${process.id} (${process.jobType}) as failed after ${runningTime} minutes`);
        } catch (error) {
          logger.error(`❌ Error updating process ${process.id}: ${error}`);
        }
      }
    }
  }
  
  // If there are health issues, send an email notification
  if (healthIssues.length > 0) {
    logger.warn(`⚠️ System health issues detected: ${healthIssues.join(', ')}`);
    
    try {
      await sendEmailNotification(EmailNotificationType.SYSTEM_HEALTH, {
        timestamp: new Date().toISOString(),
        issues: healthIssues,
        memoryUsage: resourceInfo.memory.usagePercent,
        cpuUsage: resourceInfo.cpu.usagePercent,
        actions: 'Automatic garbage collection performed'
      });
      
      logger.info(`✅ Sent system health notification email`);
    } catch (error) {
      logger.error(`❌ Error sending system health notification: ${error}`);
    }
  } else {
    logger.info(`✅ System health check passed`);
  }
}

// Run the health check if this file is executed directly
if (require.main === module) {
  checkSystemHealth()
    .then(() => {
      logger.info("✅ System health check completed");
      process.exit(0);
    })
    .catch(error => {
      logger.error(`❌ Error in system health check: ${error}`);
      process.exit(1);
    });
}

export { checkSystemHealth };

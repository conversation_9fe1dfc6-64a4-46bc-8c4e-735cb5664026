// src/routes/api/debug/notifications/+server.ts
import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { Request<PERSON>and<PERSON> } from './$types';

/**
 * Debug endpoint to check notifications in the database
 */
export const GET: RequestHandler = async () => {
  try {
    // Get all notifications from the database
    const notifications = await prisma.Notification.findMany({
      orderBy: { createdAt: 'desc' },
      take: 100,
    });

    return json({
      success: true,
      count: notifications.length,
      notifications,
    });
  } catch (error) {
    console.error('Error getting notifications for debug:', error);
    return json({ 
      error: 'Failed to get notifications',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
};

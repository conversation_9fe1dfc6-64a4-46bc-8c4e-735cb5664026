// cron/scripts/test-concurrent-jobs.ts
// Test script to simulate multiple cron jobs running concurrently

import { logger } from "../utils/logger";
import { EnhancedAdaptiveResourceManager } from "../utils/enhancedAdaptiveResourceManager";
import { WorkerPool } from "../workers/workerPool";
import os from "os";

// Function to get detailed system resource information
function getSystemResourceInfo() {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;
  
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const loadPerCpu = loadAvg / cpuCount;
  const cpuUsagePercent = loadPerCpu * 100;
  
  return {
    memory: {
      total: formatBytes(totalMemory),
      free: formatBytes(freeMemory),
      used: formatBytes(usedMemory),
      usagePercent: memoryUsagePercent.toFixed(2) + "%",
      usagePercentRaw: memoryUsagePercent
    },
    cpu: {
      count: cpuCount,
      loadAverage: loadAvg.toFixed(2),
      loadPerCpu: loadPerCpu.toFixed(2),
      usagePercent: cpuUsagePercent.toFixed(2) + "%",
      usagePercentRaw: cpuUsagePercent
    }
  };
}

// Helper function to format bytes to human-readable format
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Delay function
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Simulate a memory-intensive job
async function simulateMemoryIntensiveJob(name: string, durationMs: number, memoryMB: number) {
  logger.info(`🚀 Starting memory-intensive job: ${name} (${memoryMB}MB for ${durationMs/1000}s)`);
  
  // Allocate memory
  const arrays: Uint8Array[] = [];
  const chunkSize = 1024 * 1024; // 1MB chunks
  
  try {
    // Allocate memory in chunks to simulate memory usage
    for (let i = 0; i < memoryMB; i++) {
      arrays.push(new Uint8Array(chunkSize));
      // Fill with random data to ensure it's actually allocated
      for (let j = 0; j < chunkSize; j += 1024) {
        arrays[i][j] = Math.floor(Math.random() * 256);
      }
      
      // Log progress for larger allocations
      if (memoryMB > 100 && i % 50 === 0) {
        logger.info(`  • ${name}: Allocated ${i}MB / ${memoryMB}MB`);
      }
      
      // Small delay to prevent blocking the event loop
      if (i % 10 === 0) {
        await delay(1);
      }
    }
    
    logger.info(`✅ ${name}: Memory allocation complete (${memoryMB}MB), running for ${durationMs/1000}s`);
    
    // Hold the memory for the specified duration
    await delay(durationMs);
    
    logger.info(`✅ ${name}: Job completed successfully`);
    return true;
  } catch (error) {
    logger.error(`❌ ${name}: Job failed:`, error);
    return false;
  } finally {
    // Clear memory
    arrays.length = 0;
  }
}

// Simulate a CPU-intensive job
async function simulateCpuIntensiveJob(name: string, durationMs: number, intensity: number) {
  logger.info(`🚀 Starting CPU-intensive job: ${name} (intensity: ${intensity}, duration: ${durationMs/1000}s)`);
  
  const startTime = Date.now();
  let iterations = 0;
  
  try {
    while (Date.now() - startTime < durationMs) {
      // Perform CPU-intensive calculations
      for (let i = 0; i < intensity * 1000000; i++) {
        Math.sqrt(Math.random() * 10000);
      }
      
      iterations++;
      
      // Log progress
      if (iterations % 10 === 0) {
        const elapsedSeconds = (Date.now() - startTime) / 1000;
        logger.info(`  • ${name}: Running for ${elapsedSeconds.toFixed(1)}s / ${durationMs/1000}s (iterations: ${iterations})`);
      }
      
      // Small delay to prevent completely blocking the event loop
      await delay(100);
    }
    
    logger.info(`✅ ${name}: Job completed successfully (${iterations} iterations)`);
    return true;
  } catch (error) {
    logger.error(`❌ ${name}: Job failed:`, error);
    return false;
  }
}

// Simulate the parallel job scraper with adaptive resource management
async function simulateParallelJobScraper(durationMs: number) {
  logger.info(`🚀 Starting simulated parallel job scraper (duration: ${durationMs/1000}s)`);
  
  // Create a worker pool (simulated)
  const workerPool = {
    getCurrentMaxWorkers: () => currentWorkerCount,
    setMaxWorkers: (count: number) => {
      currentWorkerCount = count;
      logger.info(`🔄 Worker pool max workers set to ${count}`);
    },
    getTotalWorkerCount: () => currentWorkerCount
  };
  
  // Initial worker count
  let currentWorkerCount = 4;
  
  // Create the enhanced adaptive resource manager
  const resourceManager = new EnhancedAdaptiveResourceManager({
    initialWorkerCount: currentWorkerCount,
    minWorkerCount: 1,
    maxWorkerCount: 4,
    memoryThresholdPercent: 85,
    criticalMemoryThresholdPercent: 95,
    cpuThresholdPercent: 80,
    scaleDownStep: 1,
    scaleUpStep: 1,
    stabilizationPeriodMs: 30000, // 30 seconds for testing
    onWorkerCountChange: (newCount, reason) => {
      logger.info(`🔄 Worker count changed to ${newCount} (reason: ${reason})`);
      currentWorkerCount = newCount;
    }
  });
  
  const startTime = Date.now();
  let otherJobsRunning = false;
  
  // Monitor resources and adjust workers
  const monitorInterval = setInterval(() => {
    const resources = getSystemResourceInfo();
    logger.info(`📊 System resources - Memory: ${resources.memory.usagePercent}, CPU: ${resources.cpu.usagePercent}, Workers: ${currentWorkerCount}`);
    resourceManager.checkAndAdjustResources(otherJobsRunning);
  }, 5000);
  
  try {
    // Run the simulated job
    while (Date.now() - startTime < durationMs) {
      // Simulate work being done by workers
      logger.info(`🔄 Parallel job scraper running with ${currentWorkerCount} workers`);
      
      // Wait a bit
      await delay(10000);
      
      // Simulate other jobs starting
      if (Date.now() - startTime > durationMs / 3 && Date.now() - startTime < durationMs * 2/3) {
        otherJobsRunning = true;
        logger.info(`⚠️ Other jobs have started running`);
      } else {
        otherJobsRunning = false;
      }
    }
    
    logger.info(`✅ Parallel job scraper completed`);
  } finally {
    clearInterval(monitorInterval);
  }
}

// Main test function
async function testConcurrentJobs() {
  logger.info("🧪 Starting concurrent jobs test");
  
  // Log initial system resources
  const initialResources = getSystemResourceInfo();
  logger.info(`📊 Initial system resources: ${JSON.stringify(initialResources, null, 2)}`);
  
  try {
    // Start the parallel job scraper simulation (runs for 2 minutes)
    const scraperPromise = simulateParallelJobScraper(120000);
    
    // Wait a bit before starting other jobs
    await delay(30000);
    
    // Start a memory-intensive job (simulating job enrichment)
    logger.info("🚀 Starting job enrichment simulation");
    const memoryJobPromise = simulateMemoryIntensiveJob("Job Enrichment", 30000, 200);
    
    // Wait a bit before starting another job
    await delay(15000);
    
    // Start a CPU-intensive job (simulating job details scraper)
    logger.info("🚀 Starting job details scraper simulation");
    const cpuJobPromise = simulateCpuIntensiveJob("Job Details Scraper", 30000, 5);
    
    // Wait for all jobs to complete
    await Promise.all([scraperPromise, memoryJobPromise, cpuJobPromise]);
    
    // Log final system resources
    const finalResources = getSystemResourceInfo();
    logger.info(`📊 Final system resources: ${JSON.stringify(finalResources, null, 2)}`);
    
    logger.info("✅ All jobs completed successfully");
  } catch (error) {
    logger.error("❌ Test failed:", error);
  }
}

// Run the test
testConcurrentJobs()
  .then(() => {
    logger.info("✅ Concurrent jobs test completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Concurrent jobs test failed with error:", error);
    process.exit(1);
  });

#!/usr/bin/env node

/**
 * Test script to run daily summary with proper environment setup
 */

// Load environment variables from web/.env
require('dotenv').config({ path: '../web/.env' });

console.log('🔍 Environment check:');
console.log(`DATABASE_URL: ${process.env.DATABASE_URL ? 'set' : 'not set'}`);
console.log(`REDIS_HOST: ${process.env.REDIS_HOST || 'not set'}`);
console.log(`REDIS_PORT: ${process.env.REDIS_PORT || 'not set'}`);

async function testDailySummary() {
  try {
    console.log('📊 Testing daily summary generation...');
    
    // Import the daily summary function
    const { default: generateDailySummary } = await import('./jobs/dailySummary.js');
    
    // Run the daily summary
    const result = await generateDailySummary();
    
    console.log('✅ Daily summary result:', result);
    
  } catch (error) {
    console.error('❌ Daily summary test failed:', error);
    process.exit(1);
  }
}

testDailySummary();

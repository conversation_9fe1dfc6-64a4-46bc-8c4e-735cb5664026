// cron/scripts/test-circuit-breaker.ts
// Test script to diagnose circuit breaker issues

import { logger } from "../utils/logger";
import { CircuitBreaker, CircuitState } from "../utils/circuitBreaker";
import os from "os";

// Function to get detailed system resource information
function getSystemResourceInfo() {
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;
  
  const cpuCount = os.cpus().length;
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const loadPerCpu = loadAvg / cpuCount;
  const cpuUsagePercent = loadPerCpu * 100;
  
  return {
    memory: {
      total: formatBytes(totalMemory),
      free: formatBytes(freeMemory),
      used: formatBytes(usedMemory),
      usagePercent: memoryUsagePercent.toFixed(2) + "%"
    },
    cpu: {
      count: cpuCount,
      loadAverage: loadAvg.toFixed(2),
      loadPerCpu: loadPerCpu.toFixed(2),
      usagePercent: cpuUsagePercent.toFixed(2) + "%"
    },
    process: {
      pid: process.pid,
      uptime: process.uptime().toFixed(2) + "s",
      memoryUsage: {
        rss: formatBytes(process.memoryUsage().rss),
        heapTotal: formatBytes(process.memoryUsage().heapTotal),
        heapUsed: formatBytes(process.memoryUsage().heapUsed),
        external: formatBytes(process.memoryUsage().external)
      }
    },
    os: {
      platform: process.platform,
      release: os.release(),
      uptime: (os.uptime() / 60 / 60).toFixed(2) + " hours"
    }
  };
}

// Helper function to format bytes to human-readable format
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Simulate a job that would be run by the cron scheduler
async function simulateJob(name: string, circuitBreaker: CircuitBreaker) {
  logger.info(`🔄 Attempting to run job: ${name}`);
  
  // Check if circuit breaker allows execution
  if (!circuitBreaker.isClosed()) {
    logger.warn(`⚠️ Circuit breaker is ${circuitBreaker.getState()}, job execution blocked`);
    return false;
  }
  
  try {
    logger.info(`✅ Running job: ${name}`);
    // Simulate job execution
    await new Promise(resolve => setTimeout(resolve, 1000));
    logger.info(`✅ Job completed successfully: ${name}`);
    return true;
  } catch (error) {
    logger.error(`❌ Job failed: ${name}`, error);
    // Record error in circuit breaker
    circuitBreaker.recordError();
    return false;
  }
}

// Main test function
async function testCircuitBreaker() {
  logger.info("🧪 Starting circuit breaker test");
  
  // Log system resource information
  const resourceInfo = getSystemResourceInfo();
  logger.info(`📊 System resource information: ${JSON.stringify(resourceInfo, null, 2)}`);
  
  // Create circuit breaker with lower thresholds for testing
  const circuitBreaker = new CircuitBreaker({
    memoryThresholdPercent: 85,
    cpuThresholdPercent: 85,
    errorThresholdCount: 3,
    resetTimeoutMs: 10000, // 10 seconds for testing
    checkIntervalMs: 5000, // 5 seconds for testing
    onStateChange: (oldState, newState) => {
      logger.info(`🔄 Circuit breaker state changed from ${oldState} to ${newState}`);
    }
  });
  
  // Log initial state
  logger.info(`🔄 Initial circuit breaker state: ${circuitBreaker.getState()}`);
  
  // Run a series of test jobs
  for (let i = 1; i <= 5; i++) {
    logger.info(`🔄 Test iteration ${i}/5`);
    
    // Log current state before job
    logger.info(`🔄 Circuit breaker state before job: ${circuitBreaker.getState()}`);
    
    // Run test job
    const jobSuccess = await simulateJob(`TestJob-${i}`, circuitBreaker);
    
    // Log result
    logger.info(`📝 Job execution ${jobSuccess ? 'succeeded' : 'was blocked or failed'}`);
    
    // Log system resources after job
    const afterJobResources = getSystemResourceInfo();
    logger.info(`📊 System resources after job: ${JSON.stringify({
      memoryUsage: afterJobResources.memory.usagePercent,
      cpuUsage: afterJobResources.cpu.usagePercent
    })}`);
    
    // Wait between iterations
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // Test manually opening and closing the circuit
  logger.info("🧪 Testing manual circuit control");
  
  logger.info("🔴 Manually opening circuit");
  circuitBreaker.openCircuit();
  logger.info(`🔄 Circuit state after manual open: ${circuitBreaker.getState()}`);
  
  // Try to run a job while circuit is open
  const blockedJob = await simulateJob("BlockedJob", circuitBreaker);
  logger.info(`📝 Blocked job execution ${blockedJob ? 'succeeded (unexpected!)' : 'was blocked (expected)'}`);
  
  logger.info("🟢 Manually closing circuit");
  circuitBreaker.closeCircuit();
  logger.info(`🔄 Circuit state after manual close: ${circuitBreaker.getState()}`);
  
  // Try to run a job after manually closing
  const unblockJob = await simulateJob("UnblockedJob", circuitBreaker);
  logger.info(`📝 Unblocked job execution ${unblockJob ? 'succeeded (expected)' : 'was blocked (unexpected!)'}`);
  
  // Stop monitoring to allow process to exit
  logger.info("🛑 Stopping circuit breaker monitoring");
  circuitBreaker.stopMonitoring();
  
  logger.info("✅ Circuit breaker test completed");
}

// Run the test
testCircuitBreaker()
  .then(() => {
    logger.info("✅ Test completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Test failed with error:", error);
    process.exit(1);
  });

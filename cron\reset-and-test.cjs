#!/usr/bin/env node

/**
 * Reset circuit breaker and test cron job execution
 */

// Load environment variables from web/.env
require("dotenv").config({ path: "../web/.env" });

console.log("🔍 Environment check:");
console.log(`DATABASE_URL: ${process.env.DATABASE_URL ? "set" : "not set"}`);
console.log(`REDIS_HOST: ${process.env.REDIS_HOST || "not set"}`);
console.log(`REDIS_PORT: ${process.env.REDIS_PORT || "not set"}`);

async function resetAndTest() {
  try {
    console.log("🔄 Resetting circuit breaker and testing cron jobs...");

    // Import Redis
    const { redis } = await import("../workers/redis.ts");

    console.log("✅ Redis imported successfully");

    // Test Redis connection
    const pong = await redis.ping();
    console.log(`📡 Redis ping: ${pong}`);

    // Clear circuit breaker data
    console.log("🧠 Clearing circuit breaker data...");
    await redis.del("worker:circuit");
    await redis.del("cron:running_jobs");
    await redis.del("worker:health");

    // Reset worker health to healthy
    const healthData = {
      status: "healthy",
      healthy: true,
      lastHeartbeat: new Date().toISOString(),
      hostname: require("os").hostname(),
      pid: process.pid,
    };

    const workerTypes = [
      "parallelJobScraper",
      "enrichJobDetails",
      "scrapeJobDetails",
      "dailySummary",
      "marketAnalytics",
    ];

    for (const workerType of workerTypes) {
      await redis.hset("worker:health", workerType, JSON.stringify(healthData));
    }

    console.log("✅ Circuit breaker and worker health reset");

    // Check current Redis data
    const runningJobs = await redis.hgetall("cron:running_jobs");
    const healthEntries = await redis.hgetall("worker:health");
    const circuitEntries = await redis.hgetall("worker:circuit");

    console.log(`📋 Running jobs: ${Object.keys(runningJobs).length}`);
    console.log(`🏥 Health entries: ${Object.keys(healthEntries).length}`);
    console.log(`🧠 Circuit entries: ${Object.keys(circuitEntries).length}`);

    console.log("🎉 Reset completed successfully!");

    // Now try to run a simple job manually
    console.log("🧪 Testing simple job execution...");

    // Import the shared circuit breaker
    const { getSharedCircuitBreaker } = await import(
      "./utils/sharedCircuitBreaker.ts"
    );

    const circuitBreaker = getSharedCircuitBreaker();
    const isClosed = await circuitBreaker.isClosed();
    const state = circuitBreaker.getState();

    console.log(`🔧 Circuit breaker state: ${state}`);
    console.log(`🔧 Circuit breaker closed: ${isClosed}`);

    if (!isClosed) {
      console.log(
        "⚠️ Circuit breaker is OPEN - this is why cron jobs aren't running!"
      );
      console.log("🔄 Attempting to close circuit breaker...");
      circuitBreaker.closeCircuit();
      console.log("✅ Circuit breaker closed");
    }

    process.exit(0);
  } catch (error) {
    console.error("❌ Reset and test failed:", error);
    process.exit(1);
  }
}

resetAndTest();

// workers/utils/containerMetrics.ts
// Utility for reading container metrics from cgroups in containerized environments
// Specifically targeting Render's container metrics paths

import fs from "fs";
import os from "os";

/**
 * Container metrics interface
 */
export interface ContainerMetrics {
  memoryUsagePercent: number;
  cpuUsagePercent: number;
  isContainerized: boolean;
}

// Track last log time to reduce logging frequency
let lastMetricsLogTime = 0;
const LOG_INTERVAL = 30 * 60 * 1000; // 30 minutes in milliseconds

/**
 * Helper function to check if we should log based on the time interval
 * or if there are active jobs running
 */
function shouldLog(): boolean {
  const now = Date.now();

  // Always log if it's been 30 minutes since the last log
  if (now - lastMetricsLogTime >= LOG_INTERVAL) {
    return true;
  }

  // Don't log more than once per minute regardless of job status
  // This prevents excessive logging even when jobs are running
  if (now - lastMetricsLogTime < 60000) {
    // 1 minute
    return false;
  }

  // Check if there are any running jobs by looking at process arguments
  // This is a simple heuristic that works for most worker processes
  const isJobRunning = process.argv.some(
    (arg) =>
      arg.includes("job") ||
      arg.includes("worker") ||
      arg.includes("resume") ||
      arg.includes("email") ||
      arg.includes("search")
  );

  return isJobRunning;
}

/**
 * Update the last log time
 */
function updateLastLogTime(): void {
  lastMetricsLogTime = Date.now();
}

// Render-specific cgroup paths
const RENDER_CGROUP_PATHS = {
  memory: "/sys/fs/cgroup/memory.stat",
  cpu: "/sys/fs/cgroup/cpu.stat",
  io: "/sys/fs/cgroup/io.stat",
};

// Alternative cgroup paths for different container runtimes
const ALTERNATIVE_CGROUP_PATHS = {
  memory: "/sys/fs/cgroup/memory/memory.stat",
  cpu: "/sys/fs/cgroup/cpu/cpu.stat",
  io: "/sys/fs/cgroup/blkio/blkio.stat",
};

// Unified cgroup v2 paths
const UNIFIED_CGROUP_PATHS = {
  memory: "/sys/fs/cgroup/memory.current",
  memoryMax: "/sys/fs/cgroup/memory.max",
  cpu: "/sys/fs/cgroup/cpu.stat",
  io: "/sys/fs/cgroup/io.stat",
};

/**
 * Check if running in a containerized environment
 * Checks multiple possible cgroup paths to handle different container runtimes
 */
export function isContainerized(): boolean {
  try {
    // Check for Render's cgroup paths
    if (
      fs.existsSync(RENDER_CGROUP_PATHS.memory) ||
      fs.existsSync(RENDER_CGROUP_PATHS.cpu)
    ) {
      if (shouldLog()) {
        console.log(
          "Detected containerized environment using primary cgroup paths"
        );
      }
      return true;
    }

    // Check alternative cgroup paths
    if (
      fs.existsSync(ALTERNATIVE_CGROUP_PATHS.memory) ||
      fs.existsSync(ALTERNATIVE_CGROUP_PATHS.cpu)
    ) {
      if (shouldLog()) {
        console.log(
          "Detected containerized environment using alternative cgroup paths"
        );
      }
      return true;
    }

    // Check unified cgroup v2 paths
    if (
      fs.existsSync(UNIFIED_CGROUP_PATHS.memory) ||
      fs.existsSync(UNIFIED_CGROUP_PATHS.cpu)
    ) {
      if (shouldLog()) {
        console.log(
          "Detected containerized environment using unified cgroup v2 paths"
        );
      }
      return true;
    }

    // Check for .dockerenv file (common in Docker)
    if (fs.existsSync("/.dockerenv")) {
      if (shouldLog()) {
        console.log("Detected Docker container via .dockerenv file");
      }
      return true;
    }

    // Check for cgroups
    if (fs.existsSync("/proc/self/cgroup")) {
      const cgroupContent = fs.readFileSync("/proc/self/cgroup", "utf8");
      if (
        cgroupContent.includes("docker") ||
        cgroupContent.includes("kubepods") ||
        cgroupContent.includes("render")
      ) {
        if (shouldLog()) {
          console.log(
            "Detected containerized environment via /proc/self/cgroup"
          );
        }
        return true;
      }
    }

    // Check for container-specific environment variables
    if (
      process.env.KUBERNETES_SERVICE_HOST ||
      process.env.RENDER ||
      process.env.DOCKER_CONTAINER
    ) {
      if (shouldLog()) {
        console.log(
          "Detected containerized environment via environment variables"
        );
      }
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error checking if containerized: ${error}`);
    return false;
  }
}

/**
 * Parse cgroup stat file into a Map of key-value pairs
 */
function parseCgroupStat(filePath: string): Map<string, number> {
  const result = new Map<string, number>();
  try {
    const content = fs.readFileSync(filePath, "utf8");
    const lines = content.split("\n");

    for (const line of lines) {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 2) {
        const key = parts[0];
        const value = parseInt(parts[1], 10);
        if (!isNaN(value)) {
          result.set(key, value);
        }
      }
    }
  } catch (error) {
    console.error(`Error parsing cgroup stat file ${filePath}: ${error}`);
  }
  return result;
}

/**
 * Get memory usage percentage from cgroups
 * Directly reads memory.current and memory.max files to calculate usage percentage
 */
function getMemoryUsagePercent(): number | null {
  try {
    // Direct implementation of the shell script approach
    const currentPath = "/sys/fs/cgroup/memory.current";
    const maxPath = "/sys/fs/cgroup/memory.max";

    // Check if both files exist
    if (fs.existsSync(currentPath) && fs.existsSync(maxPath)) {
      if (shouldLog()) {
        console.log("Found memory.current and memory.max files");
      }

      // Read current memory usage
      const current = Number(fs.readFileSync(currentPath, "utf8").trim());

      // Read memory limit
      const maxContent = fs.readFileSync(maxPath, "utf8").trim();

      // Check if max is set to "max" (no limit)
      if (maxContent === "max") {
        if (shouldLog()) {
          console.log("No memory limit set (memory.max = max)");
        }

        // If no limit is set, use total system memory as fallback
        const totalMem = os.totalmem();

        // Calculate percentage
        const usagePercent = (current / totalMem) * 100;

        if (shouldLog()) {
          console.log(
            `Memory usage: ${usagePercent.toFixed(2)}% (using system total memory as fallback)`
          );
        }
        return usagePercent;
      } else {
        // Calculate percentage using memory.current and memory.max
        const max = Number(maxContent);

        // Validate max to avoid division by zero or NaN
        if (isNaN(max) || max <= 0) {
          if (shouldLog()) {
            console.log(
              `Invalid max memory value: ${maxContent}, using system total memory as fallback`
            );
          }
          const totalMem = os.totalmem();
          const usagePercent = (current / totalMem) * 100;
          return usagePercent;
        }

        // Calculate percentage
        const usagePercent = (current / max) * 100;

        if (shouldLog()) {
          console.log(
            `Memory usage: ${current} / ${max} = ${usagePercent.toFixed(2)}%`
          );
        }
        return usagePercent;
      }
    }

    // If direct approach fails, try alternative paths
    const alternativeCurrentPaths = [
      UNIFIED_CGROUP_PATHS.memory,
      "/sys/fs/cgroup/memory/memory.usage_in_bytes",
      "/sys/fs/cgroup/memory.usage_in_bytes",
    ];

    const alternativeMaxPaths = [
      UNIFIED_CGROUP_PATHS.memoryMax,
      "/sys/fs/cgroup/memory/memory.limit_in_bytes",
      "/sys/fs/cgroup/memory.limit_in_bytes",
    ];

    // Try each combination of paths
    for (let i = 0; i < alternativeCurrentPaths.length; i++) {
      const currPath = alternativeCurrentPaths[i];
      const limitPath = alternativeMaxPaths[i];

      if (fs.existsSync(currPath) && fs.existsSync(limitPath)) {
        if (shouldLog()) {
          console.log(
            `Found alternative memory paths: ${currPath} and ${limitPath}`
          );
        }

        // Read current memory usage
        const current = Number(fs.readFileSync(currPath, "utf8").trim());

        // Read memory limit
        const maxContent = fs.readFileSync(limitPath, "utf8").trim();

        // Check if max is set to "max" (no limit)
        if (maxContent === "max") {
          if (shouldLog()) {
            console.log("No memory limit set (memory.max = max)");
          }

          // If no limit is set, use total system memory as fallback
          const totalMem = os.totalmem();
          const usagePercent = (current / totalMem) * 100;
          if (shouldLog()) {
            console.log(
              `Memory usage: ${usagePercent.toFixed(2)}% (using system total memory as fallback)`
            );
          }
          return usagePercent;
        } else {
          // Calculate percentage using current and max
          const max = Number(maxContent);
          const usagePercent = (current / max) * 100;
          if (shouldLog()) {
            console.log(
              `Memory usage: ${current} / ${max} = ${usagePercent.toFixed(2)}%`
            );
          }
          return usagePercent;
        }
      }
    }

    // If we still couldn't get memory usage, try traditional cgroups format
    const memStatPath = RENDER_CGROUP_PATHS.memory;

    if (fs.existsSync(memStatPath)) {
      if (shouldLog()) {
        console.log(`Falling back to memory stat file: ${memStatPath}`);
      }
      const memStats = parseCgroupStat(memStatPath);

      // Extract memory usage and limit
      const memoryUsage =
        memStats.get("anon") ??
        memStats.get("total_inactive_anon") ??
        memStats.get("rss") ??
        memStats.get("active_anon") ??
        0;

      const memoryLimit =
        memStats.get("hierarchical_memory_limit") ??
        memStats.get("limit_in_bytes") ??
        0;

      // If we have both values and they're reasonable
      if (
        memoryUsage > 0 &&
        memoryLimit > 0 &&
        memoryLimit < Number.MAX_SAFE_INTEGER
      ) {
        const usagePercent = (memoryUsage / memoryLimit) * 100;
        if (shouldLog()) {
          console.log(`Memory usage from stat: ${usagePercent.toFixed(2)}%`);
        }
        return usagePercent;
      }
    }

    // If all else fails, return null
    if (shouldLog()) {
      console.log("Could not determine memory usage from cgroups");
    }
    return null;
  } catch (error) {
    console.error(`Error getting container memory usage: ${error}`);
    return null;
  }
}

/**
 * Get CPU usage percentage from cgroups
 * Uses vendor-recommended approach for Render's cgroups v2 environment
 * Returns a Promise that resolves to the CPU usage percentage
 */
function getCpuUsagePercent(): Promise<number | null> {
  return new Promise((resolve) => {
    try {
      // Check if the CPU stat file exists
      const cpuStatPath = RENDER_CGROUP_PATHS.cpu;
      if (!fs.existsSync(cpuStatPath)) {
        if (shouldLog()) {
          console.log(
            `CPU stat file not found at ${cpuStatPath}, falling back to alternative methods`
          );
        }
        resolve(fallbackCpuUsage());
        return;
      }

      // Get current CPU usage from usage_usec in cpu.stat
      const u1 = getCpuUsageUsec();
      if (u1 === null) {
        if (shouldLog()) {
          console.log("Could not get valid usage_usec value, using fallback");
        }
        resolve(fallbackCpuUsage());
        return;
      }

      // Wait 1 second for accurate measurement (as recommended by vendor)
      setTimeout(() => {
        try {
          // Get second measurement after 1 second
          const u2 = getCpuUsageUsec();
          if (u2 === null) {
            if (shouldLog()) {
              console.log(
                "Could not get valid second usage_usec value, using fallback"
              );
            }
            resolve(fallbackCpuUsage());
            return;
          }

          // Calculate difference in microseconds
          const dt = u2 - u1;

          // Get number of CPU cores
          const cpuCount = os.cpus().length;

          // Calculate per-core CPU usage (dt/1000000*100)
          const perCoreCpuPercent = (dt / 1000000) * 100;

          // Calculate all-cores percentage (dt/1000000*100/c)
          const allCoresCpuPercent = perCoreCpuPercent / cpuCount;

          if (shouldLog()) {
            console.log(
              `CPU usage: per-core: ${perCoreCpuPercent.toFixed(2)}%, all-cores: ${allCoresCpuPercent.toFixed(2)}%`
            );
          }

          // Return the per-core percentage as recommended by vendor
          resolve(Math.min(perCoreCpuPercent, 100));
        } catch (error) {
          console.error(`Error in second CPU measurement: ${error}`);
          resolve(fallbackCpuUsage());
        }
      }, 1000);
    } catch (error) {
      console.error(`Error getting container CPU usage: ${error}`);
      resolve(fallbackCpuUsage());
    }
  });
}

/**
 * Helper function to get the usage_usec value from cpu.stat
 */
function getCpuUsageUsec(): number | null {
  try {
    const cpuStatPath = RENDER_CGROUP_PATHS.cpu;
    if (!fs.existsSync(cpuStatPath)) {
      return null;
    }

    const content = fs.readFileSync(cpuStatPath, "utf8");
    const regex = /usage_usec\s+(\d+)/;
    const match = regex.exec(content);

    return match?.[1] ? parseInt(match[1], 10) : null;
  } catch (error) {
    console.error(`Error reading usage_usec: ${error}`);
    return null;
  }
}

/**
 * Fallback method to get CPU usage when cgroups data is unavailable
 * Uses OS load average as a proxy for CPU usage
 * Returns a Promise to match the signature of getCpuUsagePercent
 */
function fallbackCpuUsage(): Promise<number | null> {
  return new Promise((resolve) => {
    try {
      // Get load average and CPU count
      const loadAvg = os.loadavg()[0]; // 1-minute load average
      const cpuCount = os.cpus().length;

      // Calculate load per CPU and convert to percentage
      const loadPerCpu = loadAvg / cpuCount;
      const cpuPercent = Math.min(loadPerCpu * 100, 100); // Cap at 100%

      if (shouldLog()) {
        console.log(
          `Using fallback CPU metrics: ${cpuPercent.toFixed(2)}% (load avg: ${loadAvg}, cpus: ${cpuCount})`
        );
      }

      resolve(cpuPercent);
    } catch (error) {
      console.error(`Error getting fallback CPU metrics: ${error}`);
      resolve(null);
    }
  });
}

/**
 * Get container metrics
 * Returns null if not in a containerized environment
 * Always returns valid metrics with safe defaults if in a container
 * Now returns a Promise to handle async CPU calculations
 */
export async function getContainerMetrics(): Promise<ContainerMetrics | null> {
  try {
    // Check if we're in a containerized environment
    const containerized = isContainerized();

    if (!containerized) {
      console.log("Not in a containerized environment, returning null");
      return null;
    }

    // Get memory metrics
    let memoryUsagePercent = getMemoryUsagePercent();

    // If we couldn't get memory metrics, use OS fallback
    if (memoryUsagePercent === null) {
      try {
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        const usedMem = totalMem - freeMem;
        memoryUsagePercent = (usedMem / totalMem) * 100;

        // Only log if it's been at least LOG_INTERVAL since the last log
        if (shouldLog()) {
          console.log(
            `Using OS fallback for memory: ${memoryUsagePercent.toFixed(2)}%`
          );
        }
      } catch (osError) {
        console.error(`Error getting OS memory metrics: ${osError}`);
        memoryUsagePercent = 0; // Safe default
      }
    }

    // Get CPU metrics (now async)
    let cpuUsagePercent: number;
    try {
      // Await the CPU usage calculation
      const cpuResult = await getCpuUsagePercent();

      if (cpuResult === null) {
        // If we couldn't get CPU metrics, use OS fallback
        const fallbackResult = await fallbackCpuUsage();
        cpuUsagePercent = fallbackResult ?? 0;

        // Only log if it's been at least LOG_INTERVAL since the last log
        if (shouldLog()) {
          console.log(`Using fallback for CPU: ${cpuUsagePercent.toFixed(2)}%`);
        }
      } else {
        cpuUsagePercent = cpuResult;
      }
    } catch (cpuError) {
      console.error(`Error getting CPU metrics: ${cpuError}`);
      const fallbackResult = await fallbackCpuUsage();
      cpuUsagePercent = fallbackResult ?? 0;
    }

    // Log the metrics we're returning based on our logging rules
    if (shouldLog()) {
      // Check if there are any running jobs by looking at process arguments
      const isJobRunning = process.argv.some(
        (arg) =>
          arg.includes("job") ||
          arg.includes("worker") ||
          arg.includes("resume") ||
          arg.includes("email") ||
          arg.includes("search")
      );

      // Add job status to the log message if jobs are running
      const jobStatus = isJobRunning ? " (Jobs running)" : "";

      console.log(
        `📊 Container metrics from cgroups - Memory: ${memoryUsagePercent.toFixed(2)}%, CPU: ${cpuUsagePercent.toFixed(2)}%${jobStatus}`
      );
      updateLastLogTime();
    }

    // Ensure we don't have NaN values
    if (isNaN(memoryUsagePercent)) {
      console.warn(`Memory usage is NaN, using fallback value of 0`);
      memoryUsagePercent = 0;
    }

    if (isNaN(cpuUsagePercent)) {
      console.warn(`CPU usage is NaN, using fallback value of 0`);
      cpuUsagePercent = 0;
    }

    // Always return valid metrics with safe defaults
    return {
      memoryUsagePercent: memoryUsagePercent ?? 0,
      cpuUsagePercent: cpuUsagePercent,
      isContainerized: true,
    };
  } catch (error) {
    // Even if everything fails, return safe defaults
    console.error(`Error getting container metrics: ${error}`);
    return {
      memoryUsagePercent: 0,
      cpuUsagePercent: 0,
      isContainerized: true,
    };
  }
}
